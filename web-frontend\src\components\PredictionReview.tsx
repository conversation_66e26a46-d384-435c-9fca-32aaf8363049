import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Space,
  Button,
  DatePicker,
  Select,
  Input,
  Row,
  Col,
  Statistic,
  Progress,
  Tooltip,
  message,
  Spin
} from 'antd';
import {
  ReloadOutlined,
  SearchOutlined,
  FilterOutlined,
  DownloadOutlined,
  EyeOutlined,
  TrophyOutlined,
  AimOutlined,
  TrendingUpOutlined
} from '@ant-design/icons';
import { Line } from '@ant-design/plots';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Search } = Input;

// 数据接口定义
interface PredictionRecord {
  prediction_id: string;
  issue: string;
  predicted_numbers: string;
  actual_numbers: string | null;
  accuracy_score: number | null;
  confidence_level: number;
  prediction_date: string;
  draw_date: string | null;
  model_source: string;
  hit_positions: string[] | null;
}

interface AccuracyStats {
  total_predictions: number;
  completed_predictions: number;
  avg_accuracy: number;
  perfect_hits: number;
  by_position: {
    hundreds: { avg_accuracy: number; total_predictions: number; correct_predictions: number };
    tens: { avg_accuracy: number; total_predictions: number; correct_predictions: number };
    units: { avg_accuracy: number; total_predictions: number; correct_predictions: number };
  };
}

interface TrendData {
  date: string;
  total_predictions: number;
  average_accuracy: number;
  perfect_hit_rate: number;
}

const PredictionReview: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<PredictionRecord[]>([]);
  const [stats, setStats] = useState<AccuracyStats | null>(null);
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20, total: 0 });
  
  // 筛选状态
  const [filters, setFilters] = useState({
    dateRange: null as [dayjs.Dayjs, dayjs.Dayjs] | null,
    modelSource: '',
    hasResult: '',
    searchText: ''
  });

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize, filters]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // 获取预测记录
      const recordsResponse = await fetch('/api/prediction-review/records?' + new URLSearchParams({
        page: pagination.current.toString(),
        pageSize: pagination.pageSize.toString(),
        startDate: filters.dateRange?.[0]?.format('YYYY-MM-DD') || '',
        endDate: filters.dateRange?.[1]?.format('YYYY-MM-DD') || '',
        modelSource: filters.modelSource,
        hasResult: filters.hasResult,
        search: filters.searchText
      }));
      
      if (recordsResponse.ok) {
        const recordsData = await recordsResponse.json();
        setRecords(recordsData.records || []);
        setPagination(prev => ({ ...prev, total: recordsData.total || 0 }));
      }

      // 获取统计数据
      const statsResponse = await fetch('/api/prediction-review/accuracy-stats');
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      // 获取趋势数据
      const trendResponse = await fetch('/api/prediction-review/trends');
      if (trendResponse.ok) {
        const trendData = await trendResponse.json();
        setTrendData(trendData.daily_data || []);
      }

    } catch (error) {
      message.error('获取数据失败');
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '期号',
      dataIndex: 'issue',
      key: 'issue',
      width: 100,
      sorter: true,
    },
    {
      title: '预测号码',
      dataIndex: 'predicted_numbers',
      key: 'predicted_numbers',
      width: 100,
      render: (text: string) => (
        <Tag color="blue" style={{ fontSize: '14px', fontWeight: 'bold' }}>
          {text}
        </Tag>
      ),
    },
    {
      title: '开奖号码',
      dataIndex: 'actual_numbers',
      key: 'actual_numbers',
      width: 100,
      render: (text: string | null) => (
        text ? (
          <Tag color="green" style={{ fontSize: '14px', fontWeight: 'bold' }}>
            {text}
          </Tag>
        ) : (
          <Tag color="default">未开奖</Tag>
        )
      ),
    },
    {
      title: '准确率',
      dataIndex: 'accuracy_score',
      key: 'accuracy_score',
      width: 120,
      render: (score: number | null, record: PredictionRecord) => {
        if (score === null) return <Tag color="default">待验证</Tag>;
        
        const percentage = Math.round(score * 100);
        let color = 'red';
        if (percentage === 100) color = 'gold';
        else if (percentage >= 66) color = 'green';
        else if (percentage >= 33) color = 'orange';
        
        return (
          <Space>
            <Tag color={color}>{percentage}%</Tag>
            {score === 1.0 && <TrophyOutlined style={{ color: '#faad14' }} />}
          </Space>
        );
      },
    },
    {
      title: '命中位置',
      dataIndex: 'hit_positions',
      key: 'hit_positions',
      width: 120,
      render: (positions: string[] | null, record: PredictionRecord) => {
        if (!positions || !record.actual_numbers) return '-';
        
        const positionNames = { '0': '百', '1': '十', '2': '个' };
        return (
          <Space>
            {positions.map(pos => (
              <Tag key={pos} color="success" size="small">
                {positionNames[pos as keyof typeof positionNames]}位
              </Tag>
            ))}
          </Space>
        );
      },
    },
    {
      title: '信心度',
      dataIndex: 'confidence_level',
      key: 'confidence_level',
      width: 100,
      render: (confidence: number) => (
        <Progress
          percent={Math.round(confidence * 100)}
          size="small"
          status={confidence > 0.7 ? 'success' : confidence > 0.4 ? 'normal' : 'exception'}
        />
      ),
    },
    {
      title: '模型来源',
      dataIndex: 'model_source',
      key: 'model_source',
      width: 120,
      render: (source: string) => <Tag color="processing">{source}</Tag>,
    },
    {
      title: '预测时间',
      dataIndex: 'prediction_date',
      key: 'prediction_date',
      width: 150,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record: PredictionRecord) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (record: PredictionRecord) => {
    // TODO: 实现详情查看功能
    message.info('详情查看功能开发中');
  };

  const handleExport = () => {
    // TODO: 实现导出功能
    message.info('导出功能开发中');
  };

  // 趋势图配置
  const trendConfig = {
    data: trendData,
    xField: 'date',
    yField: 'average_accuracy',
    point: {
      size: 5,
      shape: 'diamond',
    },
    label: {
      style: {
        fill: '#aaa',
      },
    },
    tooltip: {
      formatter: (datum: TrendData) => ({
        name: '平均准确率',
        value: `${(datum.average_accuracy * 100).toFixed(1)}%`,
      }),
    },
    smooth: true,
    height: 300,
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总预测次数"
              value={stats?.total_predictions || 0}
              prefix={<AimOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已验证预测"
              value={stats?.completed_predictions || 0}
              suffix={`/ ${stats?.total_predictions || 0}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均准确率"
              value={(stats?.avg_accuracy || 0) * 100}
              precision={1}
              suffix="%"
              valueStyle={{ color: (stats?.avg_accuracy || 0) > 0.5 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="完全命中次数"
              value={stats?.perfect_hits || 0}
              prefix={<TrophyOutlined style={{ color: '#faad14' }} />}
            />
          </Card>
        </Col>
      </Row>

      {/* 趋势图 */}
      <Card title="准确率趋势" style={{ marginBottom: 24 }}>
        {trendData.length > 0 ? (
          <Line {...trendConfig} />
        ) : (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            暂无趋势数据
          </div>
        )}
      </Card>

      {/* 筛选和操作栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => setFilters(prev => ({ ...prev, dateRange: dates }))}
              placeholder={['开始日期', '结束日期']}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="模型来源"
              value={filters.modelSource}
              onChange={(value) => setFilters(prev => ({ ...prev, modelSource: value }))}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="smart_recommendation">智能推荐</Option>
              <Option value="xgboost">XGBoost</Option>
              <Option value="lightgbm">LightGBM</Option>
              <Option value="lstm">LSTM</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="验证状态"
              value={filters.hasResult}
              onChange={(value) => setFilters(prev => ({ ...prev, hasResult: value }))}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="true">已验证</Option>
              <Option value="false">未验证</Option>
            </Select>
          </Col>
          <Col span={6}>
            <Search
              placeholder="搜索期号"
              value={filters.searchText}
              onChange={(e) => setFilters(prev => ({ ...prev, searchText: e.target.value }))}
              onSearch={() => fetchData()}
              enterButton
            />
          </Col>
          <Col span={4}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchData}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 预测记录表格 */}
      <Card title="预测记录">
        <Table
          columns={columns}
          dataSource={records}
          rowKey="prediction_id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={(paginationConfig) => {
            setPagination({
              current: paginationConfig.current || 1,
              pageSize: paginationConfig.pageSize || 20,
              total: pagination.total,
            });
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default PredictionReview;
