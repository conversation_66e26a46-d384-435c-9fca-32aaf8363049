"""
反馈分析系统
创建反馈数据分析引擎，实现反馈趋势分析、问题优先级排序、改进效果评估
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, Counter
import json
import re
from dataclasses import dataclass

try:
    from ..database.feedback_data_manager import feedback_data_manager
except ImportError:
    from src.database.feedback_data_manager import feedback_data_manager

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class FeedbackTrend:
    """反馈趋势数据"""
    period: str
    feedback_count: int
    avg_rating: float
    satisfaction_score: float
    top_issues: List[str]
    improvement_areas: List[str]

@dataclass
class IssueAnalysis:
    """问题分析结果"""
    issue_id: str
    issue_type: str
    description: str
    frequency: int
    severity_score: float
    impact_score: float
    priority_score: float
    suggested_actions: List[str]
    related_features: List[str]

class FeedbackAnalytics:
    """反馈分析引擎"""
    
    def __init__(self):
        self.feedback_manager = feedback_data_manager
        logger.info("反馈分析引擎初始化完成")
    
    def analyze_feedback_trends(self, days: int = 30, granularity: str = "daily") -> List[FeedbackTrend]:
        """分析反馈趋势"""
        try:
            # 获取反馈数据
            feedback_data = self.feedback_manager.get_user_feedback(limit=1000)
            
            if not feedback_data:
                return []
            
            # 转换为DataFrame便于分析
            df = pd.DataFrame(feedback_data)
            df['created_at'] = pd.to_datetime(df['created_at'])
            
            # 过滤时间范围
            start_date = datetime.now() - timedelta(days=days)
            df = df[df['created_at'] >= start_date]
            
            # 按时间粒度分组
            if granularity == "daily":
                df['period'] = df['created_at'].dt.date
            elif granularity == "weekly":
                df['period'] = df['created_at'].dt.to_period('W')
            else:  # monthly
                df['period'] = df['created_at'].dt.to_period('M')
            
            trends = []
            for period, group in df.groupby('period'):
                # 计算基础指标
                feedback_count = len(group)
                avg_rating = group['rating'].mean() if 'rating' in group.columns else 0
                
                # 提取热门问题
                top_issues = self._extract_top_issues(group)
                
                # 识别改进领域
                improvement_areas = self._identify_improvement_areas(group)
                
                # 计算满意度评分
                satisfaction_score = self._calculate_satisfaction_score(group)
                
                trend = FeedbackTrend(
                    period=str(period),
                    feedback_count=feedback_count,
                    avg_rating=round(avg_rating, 2),
                    satisfaction_score=round(satisfaction_score, 2),
                    top_issues=top_issues,
                    improvement_areas=improvement_areas
                )
                trends.append(trend)
            
            return sorted(trends, key=lambda x: x.period)
            
        except Exception as e:
            logger.error(f"分析反馈趋势失败: {e}")
            return []
    
    def prioritize_issues(self, limit: int = 20) -> List[IssueAnalysis]:
        """问题优先级排序"""
        try:
            # 获取反馈数据
            feedback_data = self.feedback_manager.get_user_feedback(limit=500)
            
            if not feedback_data:
                return []
            
            # 分析问题
            issues = self._analyze_issues(feedback_data)
            
            # 计算优先级评分
            for issue in issues:
                issue.priority_score = self._calculate_priority_score(issue)
            
            # 按优先级排序
            issues.sort(key=lambda x: x.priority_score, reverse=True)
            
            return issues[:limit]
            
        except Exception as e:
            logger.error(f"问题优先级排序失败: {e}")
            return []
    
    def analyze_user_satisfaction(self, user_id: str = None) -> Dict[str, Any]:
        """分析用户满意度"""
        try:
            # 获取满意度调查数据
            stats = self.feedback_manager.get_feedback_statistics(days=30)
            nps_data = self.feedback_manager.calculate_nps_score(days=30)
            
            # 用户特定分析
            user_analysis = None
            if user_id:
                user_feedback = self.feedback_manager.get_user_feedback(user_id=user_id)
                user_preferences = self.feedback_manager.get_user_preferences(user_id)
                user_analysis = self._analyze_user_specific_satisfaction(user_feedback, user_preferences)
            
            return {
                'overall_stats': stats,
                'nps_analysis': nps_data,
                'user_specific': user_analysis,
                'satisfaction_segments': self._segment_satisfaction(),
                'improvement_recommendations': self._generate_improvement_recommendations(stats)
            }
            
        except Exception as e:
            logger.error(f"分析用户满意度失败: {e}")
            return {}
    
    def predict_satisfaction_impact(self, proposed_changes: List[str]) -> Dict[str, Any]:
        """预测改进措施的满意度影响"""
        try:
            # 基于历史数据预测改进效果
            historical_data = self.feedback_manager.get_feedback_statistics(days=90)
            
            impact_predictions = {}
            for change in proposed_changes:
                impact_score = self._predict_change_impact(change, historical_data)
                impact_predictions[change] = {
                    'predicted_satisfaction_increase': impact_score,
                    'confidence_level': self._calculate_prediction_confidence(change),
                    'estimated_timeframe': self._estimate_impact_timeframe(change),
                    'risk_factors': self._identify_risk_factors(change)
                }
            
            return {
                'impact_predictions': impact_predictions,
                'overall_recommendation': self._generate_overall_recommendation(impact_predictions),
                'implementation_priority': self._rank_implementation_priority(proposed_changes, impact_predictions)
            }
            
        except Exception as e:
            logger.error(f"预测满意度影响失败: {e}")
            return {}
    
    def generate_feedback_insights(self) -> Dict[str, Any]:
        """生成反馈洞察报告"""
        try:
            # 收集各种分析数据
            trends = self.analyze_feedback_trends(days=30)
            issues = self.prioritize_issues(limit=10)
            satisfaction = self.analyze_user_satisfaction()
            top_issues = self.feedback_manager.get_top_issues(limit=5)
            
            # 生成关键洞察
            key_insights = self._extract_key_insights(trends, issues, satisfaction)
            
            # 生成行动建议
            action_items = self._generate_action_items(issues, satisfaction)
            
            return {
                'summary': {
                    'total_feedback_count': sum(t.feedback_count for t in trends),
                    'avg_satisfaction': np.mean([t.satisfaction_score for t in trends]) if trends else 0,
                    'top_priority_issues': len([i for i in issues if i.priority_score > 7]),
                    'nps_score': satisfaction.get('nps_analysis', {}).get('nps_score', 0)
                },
                'trends': [asdict(t) for t in trends],
                'priority_issues': [asdict(i) for i in issues],
                'satisfaction_analysis': satisfaction,
                'top_issues': top_issues,
                'key_insights': key_insights,
                'action_items': action_items,
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"生成反馈洞察失败: {e}")
            return {}
    
    def _extract_top_issues(self, feedback_group) -> List[str]:
        """提取热门问题"""
        if feedback_group.empty:
            return []
        
        # 分析反馈内容，提取关键词
        contents = feedback_group['content'].dropna().tolist()
        
        # 简单的关键词提取（实际项目中可以使用更复杂的NLP）
        keywords = []
        for content in contents:
            words = re.findall(r'\b\w+\b', content.lower())
            keywords.extend([w for w in words if len(w) > 3])
        
        # 统计频率并返回前5个
        counter = Counter(keywords)
        return [word for word, count in counter.most_common(5)]
    
    def _identify_improvement_areas(self, feedback_group) -> List[str]:
        """识别改进领域"""
        if feedback_group.empty:
            return []
        
        improvement_areas = []
        
        # 基于反馈类型识别改进领域
        type_counts = feedback_group['feedback_type'].value_counts()
        
        for feedback_type, count in type_counts.items():
            if feedback_type == 'prediction' and count > 2:
                improvement_areas.append('预测准确性')
            elif feedback_type == 'feature' and count > 1:
                improvement_areas.append('功能易用性')
            elif feedback_type == 'suggestion':
                improvement_areas.append('功能完整性')
        
        return improvement_areas
    
    def _calculate_satisfaction_score(self, feedback_group) -> float:
        """计算满意度评分"""
        if feedback_group.empty:
            return 0.0
        
        # 基于评分计算满意度
        ratings = feedback_group['rating'].dropna()
        if len(ratings) == 0:
            return 0.0
        
        # 转换为0-100分制
        avg_rating = ratings.mean()
        satisfaction_score = (avg_rating / 5.0) * 100
        
        return satisfaction_score
    
    def _analyze_issues(self, feedback_data: List[Dict]) -> List[IssueAnalysis]:
        """分析问题"""
        issues = []
        
        # 按反馈类型和内容分组
        issue_groups = defaultdict(list)
        for feedback in feedback_data:
            key = f"{feedback['feedback_type']}_{feedback['content'][:50]}"
            issue_groups[key].append(feedback)
        
        for issue_key, feedbacks in issue_groups.items():
            if len(feedbacks) < 2:  # 只分析出现多次的问题
                continue
            
            issue_type = feedbacks[0]['feedback_type']
            description = feedbacks[0]['content']
            frequency = len(feedbacks)
            
            # 计算严重性评分
            ratings = [f['rating'] for f in feedbacks if f['rating']]
            severity_score = (5 - np.mean(ratings)) * 2 if ratings else 5  # 评分越低，严重性越高
            
            # 计算影响评分
            impact_score = min(frequency * 0.5 + severity_score * 0.5, 10)
            
            issue = IssueAnalysis(
                issue_id=issue_key,
                issue_type=issue_type,
                description=description,
                frequency=frequency,
                severity_score=round(severity_score, 2),
                impact_score=round(impact_score, 2),
                priority_score=0,  # 稍后计算
                suggested_actions=self._generate_suggested_actions(issue_type, description),
                related_features=self._identify_related_features(description)
            )
            issues.append(issue)
        
        return issues
    
    def _calculate_priority_score(self, issue: IssueAnalysis) -> float:
        """计算优先级评分"""
        # 综合考虑频率、严重性和影响
        frequency_weight = 0.4
        severity_weight = 0.3
        impact_weight = 0.3
        
        # 标准化分数
        frequency_score = min(issue.frequency / 10, 1) * 10
        
        priority_score = (
            frequency_score * frequency_weight +
            issue.severity_score * severity_weight +
            issue.impact_score * impact_weight
        )
        
        return round(priority_score, 2)
    
    def _generate_suggested_actions(self, issue_type: str, description: str) -> List[str]:
        """生成建议行动"""
        actions = []
        
        if issue_type == 'prediction':
            actions.extend([
                "优化预测算法参数",
                "增加更多历史数据训练",
                "改进特征工程"
            ])
        elif issue_type == 'feature':
            actions.extend([
                "改进用户界面设计",
                "增加功能引导",
                "优化操作流程"
            ])
        elif issue_type == 'suggestion':
            actions.extend([
                "评估功能需求",
                "制定开发计划",
                "进行用户调研"
            ])
        
        return actions
    
    def _identify_related_features(self, description: str) -> List[str]:
        """识别相关功能"""
        features = []
        
        description_lower = description.lower()
        
        if any(word in description_lower for word in ['预测', '推荐', '号码']):
            features.append('智能推荐')
        if any(word in description_lower for word in ['分析', '统计', '准确率']):
            features.append('学习验证')
        if any(word in description_lower for word in ['界面', '操作', '使用']):
            features.append('用户界面')
        if any(word in description_lower for word in ['性能', '速度', '响应']):
            features.append('系统性能')
        
        return features
    
    def _analyze_user_specific_satisfaction(self, user_feedback: List[Dict], user_preferences: Dict) -> Dict[str, Any]:
        """分析用户特定满意度"""
        if not user_feedback:
            return {}
        
        # 计算用户满意度指标
        ratings = [f['rating'] for f in user_feedback if f['rating']]
        avg_rating = np.mean(ratings) if ratings else 0
        
        # 分析反馈模式
        feedback_types = Counter([f['feedback_type'] for f in user_feedback])
        
        return {
            'avg_rating': round(avg_rating, 2),
            'feedback_count': len(user_feedback),
            'feedback_types': dict(feedback_types),
            'preferences_count': len(user_preferences),
            'engagement_level': self._calculate_engagement_level(user_feedback)
        }
    
    def _calculate_engagement_level(self, user_feedback: List[Dict]) -> str:
        """计算用户参与度"""
        feedback_count = len(user_feedback)
        
        if feedback_count >= 10:
            return "高"
        elif feedback_count >= 5:
            return "中"
        elif feedback_count >= 1:
            return "低"
        else:
            return "无"
    
    def _segment_satisfaction(self) -> Dict[str, Any]:
        """满意度分段分析"""
        # 简化版本，实际项目中可以更复杂
        return {
            'high_satisfaction': {'count': 0, 'percentage': 0},
            'medium_satisfaction': {'count': 0, 'percentage': 0},
            'low_satisfaction': {'count': 0, 'percentage': 0}
        }
    
    def _generate_improvement_recommendations(self, stats: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于统计数据生成建议
        basic_stats = stats.get('basic_stats', {})
        avg_rating = basic_stats.get('avg_rating', 0)
        
        if avg_rating < 3:
            recommendations.append("紧急改进预测准确性")
        elif avg_rating < 4:
            recommendations.append("优化用户体验")
        
        recommendations.append("持续收集用户反馈")
        recommendations.append("定期分析满意度趋势")
        
        return recommendations
    
    def _predict_change_impact(self, change: str, historical_data: Dict) -> float:
        """预测改进措施影响"""
        # 简化版本，返回模拟影响分数
        return np.random.uniform(0.1, 0.8)
    
    def _calculate_prediction_confidence(self, change: str) -> float:
        """计算预测置信度"""
        return np.random.uniform(0.6, 0.9)
    
    def _estimate_impact_timeframe(self, change: str) -> str:
        """估计影响时间框架"""
        return "2-4周"
    
    def _identify_risk_factors(self, change: str) -> List[str]:
        """识别风险因素"""
        return ["用户适应期", "技术实现复杂度"]
    
    def _generate_overall_recommendation(self, impact_predictions: Dict) -> str:
        """生成总体建议"""
        return "建议优先实施高影响、低风险的改进措施"
    
    def _rank_implementation_priority(self, changes: List[str], predictions: Dict) -> List[str]:
        """排序实施优先级"""
        return changes  # 简化版本
    
    def _extract_key_insights(self, trends: List, issues: List, satisfaction: Dict) -> List[str]:
        """提取关键洞察"""
        insights = []
        
        if trends:
            avg_satisfaction = np.mean([t.satisfaction_score for t in trends])
            if avg_satisfaction > 80:
                insights.append("用户满意度整体较高")
            elif avg_satisfaction < 60:
                insights.append("用户满意度需要改进")
        
        if issues:
            high_priority_count = len([i for i in issues if i.priority_score > 7])
            if high_priority_count > 0:
                insights.append(f"发现{high_priority_count}个高优先级问题需要处理")
        
        return insights
    
    def _generate_action_items(self, issues: List, satisfaction: Dict) -> List[Dict[str, Any]]:
        """生成行动项"""
        action_items = []
        
        # 基于高优先级问题生成行动项
        for issue in issues[:3]:  # 前3个高优先级问题
            if issue.priority_score > 7:
                action_items.append({
                    'title': f"解决{issue.issue_type}问题",
                    'description': issue.description[:100],
                    'priority': 'high',
                    'suggested_actions': issue.suggested_actions,
                    'estimated_effort': 'medium'
                })
        
        return action_items

class PersonalizationEngine:
    """个性化优化引擎"""

    def __init__(self):
        self.feedback_manager = feedback_data_manager
        logger.info("个性化优化引擎初始化完成")

    def analyze_user_behavior(self, user_id: str) -> Dict[str, Any]:
        """分析用户行为模式"""
        try:
            # 获取用户反馈和偏好
            user_feedback = self.feedback_manager.get_user_feedback(user_id=user_id, limit=100)
            user_preferences = self.feedback_manager.get_user_preferences(user_id)

            # 分析反馈模式
            feedback_patterns = self._analyze_feedback_patterns(user_feedback)

            # 分析偏好趋势
            preference_trends = self._analyze_preference_trends(user_preferences)

            # 生成用户画像
            user_profile = self._generate_user_profile(feedback_patterns, preference_trends)

            return {
                'user_id': user_id,
                'behavior_patterns': feedback_patterns,
                'preference_trends': preference_trends,
                'user_profile': user_profile,
                'personalization_score': self._calculate_personalization_score(user_feedback, user_preferences),
                'recommendations': self._generate_personalized_recommendations(user_profile)
            }
        except Exception as e:
            logger.error(f"分析用户行为失败: {e}")
            return {}

    def optimize_recommendations(self, user_id: str, base_recommendations: List[Dict]) -> List[Dict]:
        """基于用户反馈优化推荐"""
        try:
            user_analysis = self.analyze_user_behavior(user_id)
            user_profile = user_analysis.get('user_profile', {})

            # 应用个性化权重
            optimized_recommendations = []
            for rec in base_recommendations:
                optimized_rec = rec.copy()

                # 根据用户偏好调整推荐权重
                if user_profile.get('prefers_conservative', False):
                    optimized_rec['confidence_adjustment'] = -0.1
                elif user_profile.get('prefers_aggressive', False):
                    optimized_rec['confidence_adjustment'] = 0.1

                # 根据历史反馈调整
                if user_profile.get('prediction_accuracy_focus', False):
                    optimized_rec['accuracy_weight'] = 1.2

                optimized_recommendations.append(optimized_rec)

            return optimized_recommendations
        except Exception as e:
            logger.error(f"优化推荐失败: {e}")
            return base_recommendations

    def _analyze_feedback_patterns(self, user_feedback: List[Dict]) -> Dict[str, Any]:
        """分析反馈模式"""
        if not user_feedback:
            return {}

        # 反馈类型分布
        feedback_types = Counter([f['feedback_type'] for f in user_feedback])

        # 评分趋势
        ratings = [f['rating'] for f in user_feedback if f['rating']]
        avg_rating = np.mean(ratings) if ratings else 0

        # 反馈频率
        feedback_frequency = len(user_feedback) / 30  # 每月平均反馈次数

        return {
            'feedback_types': dict(feedback_types),
            'avg_rating': round(avg_rating, 2),
            'feedback_frequency': round(feedback_frequency, 2),
            'total_feedback': len(user_feedback),
            'engagement_level': 'high' if feedback_frequency > 2 else 'medium' if feedback_frequency > 0.5 else 'low'
        }

    def _analyze_preference_trends(self, user_preferences: Dict) -> Dict[str, Any]:
        """分析偏好趋势"""
        if not user_preferences:
            return {}

        trends = {}
        for pref_type, prefs in user_preferences.items():
            trends[pref_type] = {
                'count': len(prefs),
                'confidence_avg': np.mean([p['confidence_score'] for p in prefs.values()]),
                'explicit_count': len([p for p in prefs.values() if p['source'] == 'explicit']),
                'implicit_count': len([p for p in prefs.values() if p['source'] == 'implicit'])
            }

        return trends

    def _generate_user_profile(self, feedback_patterns: Dict, preference_trends: Dict) -> Dict[str, Any]:
        """生成用户画像"""
        profile = {}

        # 基于反馈模式推断用户特征
        if feedback_patterns.get('avg_rating', 0) > 4:
            profile['satisfaction_level'] = 'high'
        elif feedback_patterns.get('avg_rating', 0) > 3:
            profile['satisfaction_level'] = 'medium'
        else:
            profile['satisfaction_level'] = 'low'

        # 基于反馈类型推断关注点
        feedback_types = feedback_patterns.get('feedback_types', {})
        if feedback_types.get('prediction', 0) > feedback_types.get('feature', 0):
            profile['primary_focus'] = 'prediction_accuracy'
        else:
            profile['primary_focus'] = 'feature_usability'

        # 基于参与度推断用户类型
        engagement = feedback_patterns.get('engagement_level', 'low')
        if engagement == 'high':
            profile['user_type'] = 'power_user'
        elif engagement == 'medium':
            profile['user_type'] = 'regular_user'
        else:
            profile['user_type'] = 'casual_user'

        return profile

    def _calculate_personalization_score(self, user_feedback: List[Dict], user_preferences: Dict) -> float:
        """计算个性化评分"""
        score = 0.0

        # 基于反馈数量
        feedback_count = len(user_feedback)
        score += min(feedback_count * 0.1, 3.0)

        # 基于偏好设置数量
        preference_count = sum(len(prefs) for prefs in user_preferences.values())
        score += min(preference_count * 0.2, 2.0)

        # 基于参与度
        if feedback_count > 10:
            score += 1.0
        elif feedback_count > 5:
            score += 0.5

        return min(score, 5.0)

    def _generate_personalized_recommendations(self, user_profile: Dict) -> List[str]:
        """生成个性化建议"""
        recommendations = []

        user_type = user_profile.get('user_type', 'casual_user')
        primary_focus = user_profile.get('primary_focus', 'feature_usability')
        satisfaction_level = user_profile.get('satisfaction_level', 'medium')

        if user_type == 'power_user':
            recommendations.extend([
                "启用高级分析功能",
                "提供详细的预测解释",
                "开放更多自定义选项"
            ])
        elif user_type == 'casual_user':
            recommendations.extend([
                "简化操作流程",
                "提供更多使用指导",
                "增强默认推荐"
            ])

        if primary_focus == 'prediction_accuracy':
            recommendations.extend([
                "优先显示准确率信息",
                "提供历史准确率趋势",
                "增加预测置信度指标"
            ])

        if satisfaction_level == 'low':
            recommendations.extend([
                "主动收集改进建议",
                "提供一对一支持",
                "优先修复用户反馈的问题"
            ])

        return recommendations

# 全局反馈分析引擎实例
feedback_analytics = FeedbackAnalytics()

# 全局个性化引擎实例
personalization_engine = PersonalizationEngine()

# 辅助函数
def asdict(obj):
    """将dataclass转换为字典"""
    if hasattr(obj, '__dict__'):
        return obj.__dict__
    return obj
