"""
用户反馈管理API路由
提供反馈收集、满意度调查、反馈分析等接口
"""

from fastapi import APIRouter, HTTPException, Query, Body
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel

try:
    from ...database.feedback_data_manager import feedback_data_manager, UserFeedback, SatisfactionSurvey, UserPreference
    from ...analytics.feedback_analytics import feedback_analytics
except ImportError:
    from src.database.feedback_data_manager import feedback_data_manager, User<PERSON><PERSON>back, SatisfactionSurvey, UserPreference
    from src.analytics.feedback_analytics import feedback_analytics

router = APIRouter(prefix="/api/feedback", tags=["用户反馈"])

class FeedbackRequest(BaseModel):
    """反馈提交请求模型"""
    user_id: str
    feedback_type: str
    content: str
    rating: Optional[int] = None
    prediction_period: Optional[str] = None
    prediction_numbers: Optional[str] = None
    actual_numbers: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

class SatisfactionSurveyRequest(BaseModel):
    """满意度调查请求模型"""
    user_id: str
    overall_satisfaction: int
    prediction_accuracy_rating: int
    interface_usability_rating: int
    feature_completeness_rating: int
    recommendation_quality_rating: int
    likelihood_to_recommend: int
    comments: Optional[str] = None
    survey_version: str = "v1.0"

class UserPreferenceRequest(BaseModel):
    """用户偏好请求模型"""
    user_id: str
    preference_type: str
    preference_key: str
    preference_value: Any
    confidence_score: float = 0.0
    source: str = "explicit"

@router.post("/submit")
async def submit_feedback(request: FeedbackRequest):
    """提交用户反馈"""
    try:
        feedback = UserFeedback(
            feedback_id="",  # 将自动生成
            user_id=request.user_id,
            feedback_type=request.feedback_type,
            content=request.content,
            rating=request.rating,
            prediction_period=request.prediction_period,
            prediction_numbers=request.prediction_numbers,
            actual_numbers=request.actual_numbers,
            tags=request.tags,
            metadata=request.metadata
        )
        
        feedback_id = feedback_data_manager.create_feedback(feedback)
        
        return {
            "status": "success",
            "message": "反馈提交成功",
            "data": {
                "feedback_id": feedback_id,
                "submitted_at": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交反馈失败: {str(e)}")

@router.post("/quick")
async def submit_quick_feedback(
    user_id: str = Body(...),
    feedback_type: str = Body(...),
    content: str = Body(...),
    rating: int = Body(...)
):
    """提交快速反馈"""
    try:
        feedback = UserFeedback(
            feedback_id="",
            user_id=user_id,
            feedback_type=feedback_type,
            content=content,
            rating=rating,
            tags=["quick_feedback"]
        )
        
        feedback_id = feedback_data_manager.create_feedback(feedback)
        
        return {
            "status": "success",
            "message": "快速反馈提交成功",
            "data": {"feedback_id": feedback_id}
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交快速反馈失败: {str(e)}")

@router.post("/satisfaction-survey")
async def submit_satisfaction_survey(request: SatisfactionSurveyRequest):
    """提交满意度调查"""
    try:
        survey = SatisfactionSurvey(
            survey_id="",  # 将自动生成
            user_id=request.user_id,
            overall_satisfaction=request.overall_satisfaction,
            prediction_accuracy_rating=request.prediction_accuracy_rating,
            interface_usability_rating=request.interface_usability_rating,
            feature_completeness_rating=request.feature_completeness_rating,
            recommendation_quality_rating=request.recommendation_quality_rating,
            likelihood_to_recommend=request.likelihood_to_recommend,
            comments=request.comments,
            survey_version=request.survey_version
        )
        
        survey_id = feedback_data_manager.create_satisfaction_survey(survey)
        
        return {
            "status": "success",
            "message": "满意度调查提交成功",
            "data": {
                "survey_id": survey_id,
                "submitted_at": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交满意度调查失败: {str(e)}")

@router.get("/user-feedback")
async def get_user_feedback(
    user_id: Optional[str] = Query(None, description="用户ID"),
    feedback_type: Optional[str] = Query(None, description="反馈类型"),
    limit: int = Query(20, description="返回数量限制"),
    offset: int = Query(0, description="偏移量")
):
    """获取用户反馈"""
    try:
        feedback_list = feedback_data_manager.get_user_feedback(
            user_id=user_id,
            feedback_type=feedback_type,
            limit=limit,
            offset=offset
        )
        
        return {
            "status": "success",
            "data": feedback_list,
            "count": len(feedback_list)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户反馈失败: {str(e)}")

@router.get("/statistics")
async def get_feedback_statistics(days: int = Query(30, description="统计天数")):
    """获取反馈统计信息"""
    try:
        stats = feedback_data_manager.get_feedback_statistics(days=days)
        
        return {
            "status": "success",
            "data": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取反馈统计失败: {str(e)}")

@router.get("/nps-score")
async def get_nps_score(days: int = Query(30, description="统计天数")):
    """获取NPS评分"""
    try:
        nps_data = feedback_data_manager.calculate_nps_score(days=days)
        
        return {
            "status": "success",
            "data": nps_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取NPS评分失败: {str(e)}")

@router.get("/top-issues")
async def get_top_issues(limit: int = Query(10, description="返回数量")):
    """获取热门问题"""
    try:
        issues = feedback_data_manager.get_top_issues(limit=limit)
        
        return {
            "status": "success",
            "data": issues,
            "count": len(issues)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热门问题失败: {str(e)}")

@router.get("/trends")
async def get_feedback_trends(
    days: int = Query(30, description="分析天数"),
    granularity: str = Query("daily", description="时间粒度: daily/weekly/monthly")
):
    """获取反馈趋势分析"""
    try:
        trends = feedback_analytics.analyze_feedback_trends(days=days, granularity=granularity)
        
        return {
            "status": "success",
            "data": [trend.__dict__ for trend in trends],
            "count": len(trends)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取反馈趋势失败: {str(e)}")

@router.get("/priority-issues")
async def get_priority_issues(limit: int = Query(20, description="返回数量")):
    """获取优先级问题分析"""
    try:
        issues = feedback_analytics.prioritize_issues(limit=limit)
        
        return {
            "status": "success",
            "data": [issue.__dict__ for issue in issues],
            "count": len(issues)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取优先级问题失败: {str(e)}")

@router.get("/satisfaction-analysis")
async def get_satisfaction_analysis(user_id: Optional[str] = Query(None, description="用户ID")):
    """获取满意度分析"""
    try:
        analysis = feedback_analytics.analyze_user_satisfaction(user_id=user_id)
        
        return {
            "status": "success",
            "data": analysis
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取满意度分析失败: {str(e)}")

@router.get("/insights")
async def get_feedback_insights():
    """获取反馈洞察报告"""
    try:
        insights = feedback_analytics.generate_feedback_insights()
        
        return {
            "status": "success",
            "data": insights
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取反馈洞察失败: {str(e)}")

@router.post("/user-preference")
async def set_user_preference(request: UserPreferenceRequest):
    """设置用户偏好"""
    try:
        preference = UserPreference(
            user_id=request.user_id,
            preference_type=request.preference_type,
            preference_key=request.preference_key,
            preference_value=request.preference_value,
            confidence_score=request.confidence_score,
            source=request.source
        )
        
        feedback_data_manager.set_user_preference(preference)
        
        return {
            "status": "success",
            "message": "用户偏好设置成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置用户偏好失败: {str(e)}")

@router.get("/user-preferences/{user_id}")
async def get_user_preferences(user_id: str):
    """获取用户偏好"""
    try:
        preferences = feedback_data_manager.get_user_preferences(user_id)
        
        return {
            "status": "success",
            "data": preferences
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户偏好失败: {str(e)}")

@router.get("/survey-stats")
async def get_survey_stats():
    """获取调查统计"""
    try:
        # 获取基础统计
        stats = feedback_data_manager.get_feedback_statistics(days=30)
        nps_data = feedback_data_manager.calculate_nps_score(days=30)
        
        # 计算完成率（简化版本）
        completion_rate = 85.0  # 实际项目中应该从数据库计算
        
        survey_stats = {
            "total_surveys": nps_data.get("total_responses", 0),
            "avg_nps": nps_data.get("nps_score", 0),
            "avg_satisfaction": stats.get("satisfaction_stats", {}).get("avg_satisfaction", 0) or 0,
            "completion_rate": completion_rate
        }
        
        return {
            "status": "success",
            "data": survey_stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取调查统计失败: {str(e)}")

@router.post("/predict-impact")
async def predict_satisfaction_impact(proposed_changes: List[str] = Body(...)):
    """预测改进措施的满意度影响"""
    try:
        impact_prediction = feedback_analytics.predict_satisfaction_impact(proposed_changes)
        
        return {
            "status": "success",
            "data": impact_prediction
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"预测满意度影响失败: {str(e)}")

@router.get("/dashboard")
async def get_feedback_dashboard():
    """获取反馈管理仪表板"""
    try:
        # 获取各种统计数据
        stats = feedback_data_manager.get_feedback_statistics(days=30)
        nps_data = feedback_data_manager.calculate_nps_score(days=30)
        top_issues = feedback_data_manager.get_top_issues(limit=5)
        trends = feedback_analytics.analyze_feedback_trends(days=7, granularity="daily")
        
        dashboard_data = {
            "overview": {
                "total_feedback": stats.get("basic_stats", {}).get("total_feedback", 0),
                "avg_rating": stats.get("basic_stats", {}).get("avg_rating", 0),
                "nps_score": nps_data.get("nps_score", 0),
                "unique_users": stats.get("basic_stats", {}).get("unique_users", 0)
            },
            "feedback_distribution": stats.get("feedback_types", {}),
            "satisfaction_metrics": {
                "avg_satisfaction": stats.get("satisfaction_stats", {}).get("avg_satisfaction", 0),
                "survey_count": stats.get("satisfaction_stats", {}).get("survey_count", 0),
                "nps_breakdown": {
                    "promoters": nps_data.get("promoters", 0),
                    "passives": nps_data.get("passives", 0),
                    "detractors": nps_data.get("detractors", 0)
                }
            },
            "top_issues": top_issues,
            "recent_trends": [trend.__dict__ for trend in trends[-7:]] if trends else []
        }
        
        return {
            "status": "success",
            "data": dashboard_data,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取反馈仪表板失败: {str(e)}")
