import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Button,
  Rate,
  Input,
  message,
  Steps,
  Space,
  Typography,
  Progress,
  Divider,
  Row,
  Col,
  Statistic,
  Tag,
  Modal,
  Result
} from 'antd';
import {
  SmileOutlined,
  HeartOutlined,
  StarOutlined,
  TrophyOutlined,
  CheckCircleOutlined,
  GiftOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

interface SatisfactionData {
  overall_satisfaction: number;
  prediction_accuracy_rating: number;
  interface_usability_rating: number;
  feature_completeness_rating: number;
  recommendation_quality_rating: number;
  likelihood_to_recommend: number;
  comments?: string;
}

interface SurveyStats {
  total_surveys: number;
  avg_nps: number;
  avg_satisfaction: number;
  completion_rate: number;
}

const SatisfactionSurvey: React.FC = () => {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [surveyCompleted, setSurveyCompleted] = useState(false);
  const [surveyStats, setSurveyStats] = useState<SurveyStats | null>(null);
  const [npsScore, setNpsScore] = useState<number>(0);

  useEffect(() => {
    loadSurveyStats();
  }, []);

  const loadSurveyStats = async () => {
    try {
      const response = await axios.get('/api/feedback/survey-stats');
      setSurveyStats(response.data.data);
    } catch (error) {
      console.error('加载调查统计失败:', error);
    }
  };

  const handleSubmitSurvey = async (values: SatisfactionData) => {
    setLoading(true);
    try {
      const surveyData = {
        ...values,
        user_id: 'current_user', // 实际项目中从用户上下文获取
        survey_version: 'v1.0'
      };

      await axios.post('/api/feedback/satisfaction-survey', surveyData);
      
      message.success('满意度调查提交成功，感谢您的参与！');
      setSurveyCompleted(true);
      loadSurveyStats();
    } catch (error) {
      console.error('提交满意度调查失败:', error);
      message.error('提交失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    form.validateFields().then(() => {
      setCurrentStep(currentStep + 1);
    }).catch(() => {
      message.warning('请完成当前步骤的评分');
    });
  };

  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  const calculateNPS = (score: number) => {
    setNpsScore(score);
    // NPS分类：0-6为贬损者，7-8为被动者，9-10为推荐者
    if (score >= 9) return 'promoter';
    if (score >= 7) return 'passive';
    return 'detractor';
  };

  const getNPSColor = (score: number) => {
    if (score >= 9) return '#52c41a';
    if (score >= 7) return '#faad14';
    return '#ff4d4f';
  };

  const getNPSLabel = (score: number) => {
    if (score >= 9) return '推荐者';
    if (score >= 7) return '被动者';
    return '贬损者';
  };

  const renderStep1 = () => (
    <div>
      <Title level={4}>
        <SmileOutlined style={{ color: '#1890ff', marginRight: 8 }} />
        整体满意度评价
      </Title>
      <Paragraph type="secondary">
        请对福彩3D智能预测系统的整体使用体验进行评分
      </Paragraph>

      <Form.Item
        name="overall_satisfaction"
        label="整体满意度 (1-10分)"
        rules={[{ required: true, message: '请进行整体满意度评分' }]}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Rate
            count={10}
            allowHalf
            character={<SmileOutlined />}
            style={{ fontSize: 24, color: '#1890ff' }}
          />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">1分=非常不满意，10分=非常满意</Text>
          </div>
        </div>
      </Form.Item>

      <Form.Item
        name="likelihood_to_recommend"
        label="推荐意愿 (NPS评分)"
        rules={[{ required: true, message: '请进行推荐意愿评分' }]}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Rate
            count={10}
            allowHalf
            character={<HeartOutlined />}
            style={{ fontSize: 24, color: '#ff6b6b' }}
            onChange={calculateNPS}
          />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">您向朋友推荐本系统的可能性有多大？</Text>
            {npsScore > 0 && (
              <div style={{ marginTop: 8 }}>
                <Tag color={getNPSColor(npsScore)}>
                  {getNPSLabel(npsScore)}
                </Tag>
              </div>
            )}
          </div>
        </div>
      </Form.Item>
    </div>
  );

  const renderStep2 = () => (
    <div>
      <Title level={4}>
        <StarOutlined style={{ color: '#faad14', marginRight: 8 }} />
        功能模块评价
      </Title>
      <Paragraph type="secondary">
        请对系统各个功能模块的表现进行评分
      </Paragraph>

      <Row gutter={[16, 24]}>
        <Col span={12}>
          <Card size="small">
            <Form.Item
              name="prediction_accuracy_rating"
              label="预测准确性"
              rules={[{ required: true, message: '请评分预测准确性' }]}
            >
              <Rate
                character={<TrophyOutlined />}
                style={{ color: '#faad14' }}
              />
            </Form.Item>
          </Card>
        </Col>

        <Col span={12}>
          <Card size="small">
            <Form.Item
              name="interface_usability_rating"
              label="界面易用性"
              rules={[{ required: true, message: '请评分界面易用性' }]}
            >
              <Rate
                character={<ThunderboltOutlined />}
                style={{ color: '#1890ff' }}
              />
            </Form.Item>
          </Card>
        </Col>

        <Col span={12}>
          <Card size="small">
            <Form.Item
              name="feature_completeness_rating"
              label="功能完整性"
              rules={[{ required: true, message: '请评分功能完整性' }]}
            >
              <Rate
                character={<CheckCircleOutlined />}
                style={{ color: '#52c41a' }}
              />
            </Form.Item>
          </Card>
        </Col>

        <Col span={12}>
          <Card size="small">
            <Form.Item
              name="recommendation_quality_rating"
              label="推荐质量"
              rules={[{ required: true, message: '请评分推荐质量' }]}
            >
              <Rate
                character={<GiftOutlined />}
                style={{ color: '#eb2f96' }}
              />
            </Form.Item>
          </Card>
        </Col>
      </Row>
    </div>
  );

  const renderStep3 = () => (
    <div>
      <Title level={4}>
        <HeartOutlined style={{ color: '#eb2f96', marginRight: 8 }} />
        意见与建议
      </Title>
      <Paragraph type="secondary">
        请分享您的使用感受和改进建议（可选）
      </Paragraph>

      <Form.Item
        name="comments"
        label="详细意见"
      >
        <TextArea
          rows={6}
          placeholder="请分享您的使用体验、遇到的问题或改进建议..."
          showCount
          maxLength={1000}
        />
      </Form.Item>

      <Card size="small" style={{ background: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <Space>
          <GiftOutlined style={{ color: '#52c41a' }} />
          <Text>
            <strong>感谢参与！</strong>
            您的反馈将帮助我们持续改进系统，为您提供更好的预测服务。
          </Text>
        </Space>
      </Card>
    </div>
  );

  const renderSurveyStats = () => {
    if (!surveyStats) return null;

    return (
      <Card style={{ marginBottom: 24 }}>
        <Title level={4}>调查统计</Title>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="参与人数"
              value={surveyStats.total_surveys}
              prefix={<SmileOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="平均NPS"
              value={surveyStats.avg_nps}
              precision={1}
              prefix={<HeartOutlined />}
              valueStyle={{ color: getNPSColor(surveyStats.avg_nps) }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="平均满意度"
              value={surveyStats.avg_satisfaction}
              precision={1}
              suffix="/ 10"
              prefix={<StarOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="完成率"
              value={surveyStats.completion_rate}
              precision={1}
              suffix="%"
              prefix={<TrophyOutlined />}
            />
          </Col>
        </Row>
      </Card>
    );
  };

  if (surveyCompleted) {
    return (
      <div style={{ maxWidth: 600, margin: '0 auto', padding: '40px 20px' }}>
        <Result
          status="success"
          title="满意度调查提交成功！"
          subTitle="感谢您的宝贵意见，我们将根据您的反馈持续改进系统。"
          extra={[
            <Button type="primary" key="back" onClick={() => setSurveyCompleted(false)}>
              重新评价
            </Button>
          ]}
        />
        {renderSurveyStats()}
      </div>
    );
  }

  const steps = [
    {
      title: '整体评价',
      content: renderStep1(),
    },
    {
      title: '功能评分',
      content: renderStep2(),
    },
    {
      title: '意见建议',
      content: renderStep3(),
    },
  ];

  return (
    <div style={{ maxWidth: 800, margin: '0 auto', padding: '20px' }}>
      {renderSurveyStats()}

      <Card>
        <Title level={3}>
          <SmileOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          满意度调查
        </Title>
        <Paragraph type="secondary">
          您的评价对我们非常重要，请花几分钟时间完成这份满意度调查。
        </Paragraph>

        <Steps current={currentStep} style={{ marginBottom: 32 }}>
          {steps.map(item => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <div style={{ marginBottom: 24 }}>
          <Progress
            percent={((currentStep + 1) / steps.length) * 100}
            showInfo={false}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmitSurvey}
        >
          <div style={{ minHeight: 400 }}>
            {steps[currentStep].content}
          </div>

          <Divider />

          <div style={{ textAlign: 'center' }}>
            <Space>
              {currentStep > 0 && (
                <Button onClick={handlePrev}>
                  上一步
                </Button>
              )}
              {currentStep < steps.length - 1 && (
                <Button type="primary" onClick={handleNext}>
                  下一步
                </Button>
              )}
              {currentStep === steps.length - 1 && (
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<CheckCircleOutlined />}
                >
                  提交调查
                </Button>
              )}
            </Space>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default SatisfactionSurvey;
