"""
性能监控API路由
提供性能数据查询和监控功能
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Optional, Dict, Any
import psutil
import asyncio
from datetime import datetime, timedelta

try:
    from ...monitoring.performance_monitor import performance_monitor
except ImportError:
    from src.monitoring.performance_monitor import performance_monitor

router = APIRouter(prefix="/api/performance", tags=["性能监控"])

@router.get("/summary")
async def get_performance_summary(hours: int = Query(24, description="统计时间范围（小时）")):
    """获取性能摘要"""
    try:
        summary = performance_monitor.get_api_performance_summary(hours=hours)
        return {
            "status": "success",
            "data": summary,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能摘要失败: {str(e)}")

@router.get("/system")
async def get_system_metrics():
    """获取当前系统性能指标"""
    try:
        # 获取系统资源使用情况
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 获取网络连接数（简化版）
        try:
            connections = len(psutil.net_connections())
        except:
            connections = 0
        
        metrics = {
            "cpu_usage": cpu_usage,
            "memory_usage": memory.percent,
            "memory_total": memory.total,
            "memory_available": memory.available,
            "disk_usage": disk.percent,
            "disk_total": disk.total,
            "disk_free": disk.free,
            "active_connections": connections,
            "timestamp": datetime.now().isoformat()
        }
        
        # 记录到性能监控系统
        performance_monitor.track_system_metrics(
            cpu_usage=cpu_usage,
            memory_usage=memory.percent,
            disk_usage=disk.percent,
            active_connections=connections
        )
        
        return {
            "status": "success",
            "data": metrics
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统指标失败: {str(e)}")

@router.get("/api-stats")
async def get_api_statistics(
    endpoint: Optional[str] = Query(None, description="特定API端点"),
    hours: int = Query(24, description="统计时间范围（小时）")
):
    """获取API统计信息"""
    try:
        # 获取最近的API指标
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [m for m in performance_monitor.api_metrics if m.timestamp > cutoff_time]
        
        if endpoint:
            recent_metrics = [m for m in recent_metrics if m.endpoint == endpoint]
        
        if not recent_metrics:
            return {
                "status": "success",
                "data": {
                    "message": "暂无数据",
                    "total_requests": 0
                }
            }
        
        # 统计分析
        total_requests = len(recent_metrics)
        avg_response_time = sum(m.response_time for m in recent_metrics) / total_requests
        max_response_time = max(m.response_time for m in recent_metrics)
        min_response_time = min(m.response_time for m in recent_metrics)
        
        # 错误统计
        error_count = sum(1 for m in recent_metrics if m.status_code >= 400)
        error_rate = error_count / total_requests * 100 if total_requests > 0 else 0
        
        # 状态码分布
        status_codes = {}
        for metric in recent_metrics:
            status_codes[metric.status_code] = status_codes.get(metric.status_code, 0) + 1
        
        # 响应时间分布
        response_time_buckets = {
            "fast": 0,      # < 0.5s
            "normal": 0,    # 0.5s - 2s
            "slow": 0,      # 2s - 5s
            "very_slow": 0  # > 5s
        }
        
        for metric in recent_metrics:
            if metric.response_time < 0.5:
                response_time_buckets["fast"] += 1
            elif metric.response_time < 2.0:
                response_time_buckets["normal"] += 1
            elif metric.response_time < 5.0:
                response_time_buckets["slow"] += 1
            else:
                response_time_buckets["very_slow"] += 1
        
        return {
            "status": "success",
            "data": {
                "period_hours": hours,
                "endpoint": endpoint,
                "total_requests": total_requests,
                "avg_response_time": round(avg_response_time, 3),
                "max_response_time": round(max_response_time, 3),
                "min_response_time": round(min_response_time, 3),
                "error_count": error_count,
                "error_rate": round(error_rate, 2),
                "status_codes": status_codes,
                "response_time_distribution": response_time_buckets
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取API统计失败: {str(e)}")

@router.get("/health")
async def get_health_status():
    """获取系统健康状态"""
    try:
        # 获取系统指标
        cpu_usage = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 健康状态评估
        health_score = 100
        issues = []
        
        if cpu_usage > 80:
            health_score -= 20
            issues.append(f"CPU使用率过高: {cpu_usage:.1f}%")
        
        if memory.percent > 85:
            health_score -= 20
            issues.append(f"内存使用率过高: {memory.percent:.1f}%")
        
        if disk.percent > 90:
            health_score -= 15
            issues.append(f"磁盘使用率过高: {disk.percent:.1f}%")
        
        # 检查最近的API错误率
        recent_summary = performance_monitor.get_api_performance_summary(hours=1)
        if isinstance(recent_summary, dict) and "error_rate" in recent_summary:
            if recent_summary["error_rate"] > 10:
                health_score -= 25
                issues.append(f"API错误率过高: {recent_summary['error_rate']:.1f}%")
        
        # 确定健康状态
        if health_score >= 90:
            status = "healthy"
            color = "green"
        elif health_score >= 70:
            status = "warning"
            color = "orange"
        else:
            status = "critical"
            color = "red"
        
        return {
            "status": "success",
            "data": {
                "health_status": status,
                "health_score": max(0, health_score),
                "color": color,
                "issues": issues,
                "system_metrics": {
                    "cpu_usage": cpu_usage,
                    "memory_usage": memory.percent,
                    "disk_usage": disk.percent
                },
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取健康状态失败: {str(e)}")

@router.post("/track-user-behavior")
async def track_user_behavior(
    user_id: str,
    action: str,
    page: str,
    session_id: str,
    additional_data: Optional[Dict[str, Any]] = None
):
    """记录用户行为"""
    try:
        performance_monitor.track_user_behavior(
            user_id=user_id,
            action=action,
            page=page,
            session_id=session_id,
            additional_data=additional_data
        )
        
        return {
            "status": "success",
            "message": "用户行为记录成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"记录用户行为失败: {str(e)}")

@router.get("/alerts")
async def get_performance_alerts():
    """获取性能报警信息"""
    try:
        alerts = []
        
        # 检查系统资源
        cpu_usage = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        if cpu_usage > 80:
            alerts.append({
                "type": "system",
                "level": "warning" if cpu_usage < 90 else "critical",
                "message": f"CPU使用率过高: {cpu_usage:.1f}%",
                "timestamp": datetime.now().isoformat()
            })
        
        if memory.percent > 85:
            alerts.append({
                "type": "system",
                "level": "warning" if memory.percent < 95 else "critical",
                "message": f"内存使用率过高: {memory.percent:.1f}%",
                "timestamp": datetime.now().isoformat()
            })
        
        if disk.percent > 90:
            alerts.append({
                "type": "system",
                "level": "critical",
                "message": f"磁盘空间不足: {disk.percent:.1f}%",
                "timestamp": datetime.now().isoformat()
            })
        
        # 检查API性能
        recent_summary = performance_monitor.get_api_performance_summary(hours=1)
        if isinstance(recent_summary, dict):
            if recent_summary.get("error_rate", 0) > 10:
                alerts.append({
                    "type": "api",
                    "level": "warning",
                    "message": f"API错误率过高: {recent_summary['error_rate']:.1f}%",
                    "timestamp": datetime.now().isoformat()
                })
            
            if recent_summary.get("avg_response_time", 0) > 3:
                alerts.append({
                    "type": "api",
                    "level": "warning",
                    "message": f"API响应时间过长: {recent_summary['avg_response_time']:.2f}s",
                    "timestamp": datetime.now().isoformat()
                })
        
        return {
            "status": "success",
            "data": {
                "alerts": alerts,
                "alert_count": len(alerts),
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取报警信息失败: {str(e)}")
