"""
功能开关配置管理系统
实现A/B测试配置管理、个性化配置支持、动态配置更新
"""

import json
import logging
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import hashlib
import random

# 配置日志
logger = logging.getLogger(__name__)

class FeatureStatus(Enum):
    """功能状态枚举"""
    ENABLED = "enabled"
    DISABLED = "disabled"
    TESTING = "testing"
    ROLLOUT = "rollout"

class ConfigScope(Enum):
    """配置作用域"""
    GLOBAL = "global"
    USER = "user"
    SESSION = "session"
    AB_TEST = "ab_test"

@dataclass
class FeatureConfig:
    """功能配置"""
    name: str
    status: FeatureStatus
    scope: ConfigScope
    value: Any = None
    description: str = ""
    created_at: datetime = None
    updated_at: datetime = None
    expires_at: datetime = None
    conditions: Dict[str, Any] = None
    rollout_percentage: float = 0.0
    ab_test_group: str = None

@dataclass
class ABTestConfig:
    """A/B测试配置"""
    test_name: str
    description: str
    start_date: datetime
    end_date: datetime
    traffic_percentage: float
    groups: Dict[str, Dict[str, Any]]
    success_metrics: List[str]
    status: str = "active"

class FeatureConfigManager:
    """功能配置管理器"""
    
    def __init__(self, db_path: str = "data/feature_config.db"):
        self.db_path = db_path
        self._config_cache: Dict[str, FeatureConfig] = {}
        self._ab_tests: Dict[str, ABTestConfig] = {}
        self._user_assignments: Dict[str, Dict[str, str]] = {}
        self._cache_lock = threading.RLock()
        self._subscribers: Dict[str, List[Callable]] = {}
        
        # 初始化数据库
        self._init_database()
        
        # 加载配置
        self._load_configs()
        
        # 启动后台任务
        self._start_background_tasks()
        
        logger.info("功能配置管理器初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            # 功能配置表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS feature_configs (
                    name TEXT PRIMARY KEY,
                    status TEXT NOT NULL,
                    scope TEXT NOT NULL,
                    value TEXT,
                    description TEXT,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL,
                    expires_at DATETIME,
                    conditions TEXT,
                    rollout_percentage REAL DEFAULT 0.0,
                    ab_test_group TEXT
                )
            """)
            
            # A/B测试表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS ab_tests (
                    test_name TEXT PRIMARY KEY,
                    description TEXT,
                    start_date DATETIME NOT NULL,
                    end_date DATETIME NOT NULL,
                    traffic_percentage REAL NOT NULL,
                    groups TEXT NOT NULL,
                    success_metrics TEXT,
                    status TEXT DEFAULT 'active',
                    created_at DATETIME NOT NULL
                )
            """)
            
            # 用户分组表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_assignments (
                    user_id TEXT NOT NULL,
                    test_name TEXT NOT NULL,
                    group_name TEXT NOT NULL,
                    assigned_at DATETIME NOT NULL,
                    PRIMARY KEY (user_id, test_name)
                )
            """)
            
            # 配置历史表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS config_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_name TEXT NOT NULL,
                    old_value TEXT,
                    new_value TEXT,
                    changed_by TEXT,
                    changed_at DATETIME NOT NULL,
                    reason TEXT
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_config_scope ON feature_configs(scope)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_ab_test_status ON ab_tests(status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_user_assignments ON user_assignments(user_id)")
    
    def _load_configs(self):
        """加载配置"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # 加载功能配置
            cursor = conn.execute("SELECT * FROM feature_configs")
            for row in cursor.fetchall():
                config = FeatureConfig(
                    name=row['name'],
                    status=FeatureStatus(row['status']),
                    scope=ConfigScope(row['scope']),
                    value=json.loads(row['value']) if row['value'] else None,
                    description=row['description'],
                    created_at=datetime.fromisoformat(row['created_at']),
                    updated_at=datetime.fromisoformat(row['updated_at']),
                    expires_at=datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None,
                    conditions=json.loads(row['conditions']) if row['conditions'] else None,
                    rollout_percentage=row['rollout_percentage'],
                    ab_test_group=row['ab_test_group']
                )
                self._config_cache[config.name] = config
            
            # 加载A/B测试
            cursor = conn.execute("SELECT * FROM ab_tests")
            for row in cursor.fetchall():
                ab_test = ABTestConfig(
                    test_name=row['test_name'],
                    description=row['description'],
                    start_date=datetime.fromisoformat(row['start_date']),
                    end_date=datetime.fromisoformat(row['end_date']),
                    traffic_percentage=row['traffic_percentage'],
                    groups=json.loads(row['groups']),
                    success_metrics=json.loads(row['success_metrics']) if row['success_metrics'] else [],
                    status=row['status']
                )
                self._ab_tests[ab_test.test_name] = ab_test
    
    def is_feature_enabled(self, feature_name: str, user_id: str = None, context: Dict[str, Any] = None) -> bool:
        """检查功能是否启用"""
        with self._cache_lock:
            config = self._config_cache.get(feature_name)
            if not config:
                return False
            
            # 检查过期时间
            if config.expires_at and datetime.now() > config.expires_at:
                return False
            
            # 检查基本状态
            if config.status == FeatureStatus.DISABLED:
                return False
            elif config.status == FeatureStatus.ENABLED:
                return True
            
            # 检查条件
            if config.conditions and not self._check_conditions(config.conditions, context):
                return False
            
            # 检查渐进式发布
            if config.status == FeatureStatus.ROLLOUT:
                return self._check_rollout(config, user_id)
            
            # 检查A/B测试
            if config.status == FeatureStatus.TESTING and config.ab_test_group:
                return self._check_ab_test(config, user_id)
            
            return False
    
    def get_feature_value(self, feature_name: str, default_value: Any = None, user_id: str = None, context: Dict[str, Any] = None) -> Any:
        """获取功能配置值"""
        if not self.is_feature_enabled(feature_name, user_id, context):
            return default_value
        
        with self._cache_lock:
            config = self._config_cache.get(feature_name)
            if config and config.value is not None:
                return config.value
            
            return default_value
    
    def _check_conditions(self, conditions: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """检查条件是否满足"""
        if not context:
            return True
        
        for key, expected_value in conditions.items():
            if key not in context:
                return False
            
            actual_value = context[key]
            
            # 支持多种条件类型
            if isinstance(expected_value, dict):
                operator = expected_value.get('operator', 'eq')
                value = expected_value.get('value')
                
                if operator == 'eq' and actual_value != value:
                    return False
                elif operator == 'ne' and actual_value == value:
                    return False
                elif operator == 'gt' and actual_value <= value:
                    return False
                elif operator == 'lt' and actual_value >= value:
                    return False
                elif operator == 'in' and actual_value not in value:
                    return False
                elif operator == 'not_in' and actual_value in value:
                    return False
            else:
                if actual_value != expected_value:
                    return False
        
        return True
    
    def _check_rollout(self, config: FeatureConfig, user_id: str) -> bool:
        """检查渐进式发布"""
        if not user_id:
            return random.random() < config.rollout_percentage / 100.0
        
        # 基于用户ID的一致性哈希
        hash_value = int(hashlib.md5(f"{config.name}:{user_id}".encode()).hexdigest(), 16)
        return (hash_value % 100) < config.rollout_percentage
    
    def _check_ab_test(self, config: FeatureConfig, user_id: str) -> bool:
        """检查A/B测试"""
        if not user_id or not config.ab_test_group:
            return False
        
        ab_test = self._ab_tests.get(config.ab_test_group)
        if not ab_test:
            return False
        
        # 检查测试是否在有效期内
        now = datetime.now()
        if now < ab_test.start_date or now > ab_test.end_date:
            return False
        
        # 获取用户分组
        group = self._get_user_ab_group(user_id, ab_test.test_name)
        if not group:
            return False
        
        # 检查分组配置
        group_config = ab_test.groups.get(group, {})
        return group_config.get(config.name, False)
    
    def _get_user_ab_group(self, user_id: str, test_name: str) -> Optional[str]:
        """获取用户A/B测试分组"""
        # 先检查缓存
        if user_id in self._user_assignments and test_name in self._user_assignments[user_id]:
            return self._user_assignments[user_id][test_name]
        
        # 从数据库查询
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT group_name FROM user_assignments WHERE user_id = ? AND test_name = ?",
                (user_id, test_name)
            )
            row = cursor.fetchone()
            if row:
                group = row[0]
                # 更新缓存
                if user_id not in self._user_assignments:
                    self._user_assignments[user_id] = {}
                self._user_assignments[user_id][test_name] = group
                return group
        
        # 如果没有分组，进行分组分配
        return self._assign_user_to_group(user_id, test_name)
    
    def _assign_user_to_group(self, user_id: str, test_name: str) -> Optional[str]:
        """为用户分配A/B测试分组"""
        ab_test = self._ab_tests.get(test_name)
        if not ab_test:
            return None
        
        # 检查是否在流量范围内
        hash_value = int(hashlib.md5(f"{test_name}:{user_id}".encode()).hexdigest(), 16)
        if (hash_value % 100) >= ab_test.traffic_percentage:
            return None
        
        # 分配分组
        groups = list(ab_test.groups.keys())
        if not groups:
            return None
        
        group_index = hash_value % len(groups)
        assigned_group = groups[group_index]
        
        # 保存分配结果
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                "INSERT OR REPLACE INTO user_assignments (user_id, test_name, group_name, assigned_at) VALUES (?, ?, ?, ?)",
                (user_id, test_name, assigned_group, datetime.now())
            )
        
        # 更新缓存
        if user_id not in self._user_assignments:
            self._user_assignments[user_id] = {}
        self._user_assignments[user_id][test_name] = assigned_group
        
        return assigned_group
    
    def set_feature_config(self, config: FeatureConfig, changed_by: str = "system", reason: str = ""):
        """设置功能配置"""
        with self._cache_lock:
            old_config = self._config_cache.get(config.name)
            old_value = old_config.value if old_config else None
            
            # 更新时间戳
            config.updated_at = datetime.now()
            if not config.created_at:
                config.created_at = config.updated_at
            
            # 保存到数据库
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO feature_configs 
                    (name, status, scope, value, description, created_at, updated_at, expires_at, conditions, rollout_percentage, ab_test_group)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    config.name,
                    config.status.value,
                    config.scope.value,
                    json.dumps(config.value) if config.value is not None else None,
                    config.description,
                    config.created_at,
                    config.updated_at,
                    config.expires_at,
                    json.dumps(config.conditions) if config.conditions else None,
                    config.rollout_percentage,
                    config.ab_test_group
                ))
                
                # 记录历史
                conn.execute("""
                    INSERT INTO config_history (config_name, old_value, new_value, changed_by, changed_at, reason)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    config.name,
                    json.dumps(old_value) if old_value is not None else None,
                    json.dumps(config.value) if config.value is not None else None,
                    changed_by,
                    datetime.now(),
                    reason
                ))
            
            # 更新缓存
            self._config_cache[config.name] = config
            
            # 通知订阅者
            self._notify_subscribers(config.name, config)
    
    def create_ab_test(self, ab_test: ABTestConfig):
        """创建A/B测试"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO ab_tests 
                (test_name, description, start_date, end_date, traffic_percentage, groups, success_metrics, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                ab_test.test_name,
                ab_test.description,
                ab_test.start_date,
                ab_test.end_date,
                ab_test.traffic_percentage,
                json.dumps(ab_test.groups),
                json.dumps(ab_test.success_metrics),
                ab_test.status,
                datetime.now()
            ))
        
        self._ab_tests[ab_test.test_name] = ab_test
    
    def subscribe(self, feature_name: str, callback: Callable[[FeatureConfig], None]):
        """订阅配置变更"""
        if feature_name not in self._subscribers:
            self._subscribers[feature_name] = []
        self._subscribers[feature_name].append(callback)
    
    def _notify_subscribers(self, feature_name: str, config: FeatureConfig):
        """通知订阅者"""
        for callback in self._subscribers.get(feature_name, []):
            try:
                callback(config)
            except Exception as e:
                logger.error(f"通知订阅者失败: {e}")
    
    def _start_background_tasks(self):
        """启动后台任务"""
        threading.Thread(target=self._cleanup_worker, daemon=True).start()
    
    def _cleanup_worker(self):
        """清理工作线程"""
        while True:
            try:
                time.sleep(3600)  # 每小时清理一次
                
                now = datetime.now()
                
                # 清理过期配置
                expired_configs = []
                with self._cache_lock:
                    for name, config in self._config_cache.items():
                        if config.expires_at and now > config.expires_at:
                            expired_configs.append(name)
                
                for name in expired_configs:
                    del self._config_cache[name]
                
                # 清理过期A/B测试
                expired_tests = []
                for name, ab_test in self._ab_tests.items():
                    if now > ab_test.end_date:
                        expired_tests.append(name)
                
                for name in expired_tests:
                    self._ab_tests[name].status = "expired"
                
                logger.debug("配置清理完成")
                
            except Exception as e:
                logger.error(f"配置清理失败: {e}")
    
    def get_all_configs(self) -> Dict[str, FeatureConfig]:
        """获取所有配置"""
        with self._cache_lock:
            return self._config_cache.copy()
    
    def get_ab_test_stats(self, test_name: str) -> Dict[str, Any]:
        """获取A/B测试统计"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            cursor = conn.execute("""
                SELECT group_name, COUNT(*) as user_count
                FROM user_assignments 
                WHERE test_name = ?
                GROUP BY group_name
            """, (test_name,))
            
            group_stats = {row['group_name']: row['user_count'] for row in cursor.fetchall()}
            
            return {
                'test_name': test_name,
                'group_distribution': group_stats,
                'total_users': sum(group_stats.values())
            }

# 全局配置管理器实例
feature_config_manager = FeatureConfigManager()
