import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Select,
  Input,
  Button,
  Table,
  Alert,
  Spin,
  Typography,
  Space,
  Tag,
  Progress,
  Divider,
  message,
  Tabs
} from 'antd'
import type { TabsProps } from 'antd'
import {
  ExperimentOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  Check<PERSON><PERSON>cleOutlined,
  Exclamation<PERSON><PERSON>cleOutlined,
  Question<PERSON>ircleOutlined,
  <PERSON>Outlined,
  <PERSON>boltOutlined,
  HistoryOutlined,
  SwapOutlined
} from '@ant-design/icons'
import { apiRequest } from '../utils/api'
import ShapUserGuide from './ShapUserGuide'
import SmartRecommendationCards from './SmartRecommendationCards'
import AccuracyAnalytics from './AccuracyAnalytics'
import StrategyComparison from './StrategyComparison'
import PredictionReview from './PredictionReview'

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { TabPane } = Tabs

interface ShapStatus {
  shap_available: boolean
  explainers_count: number
  feature_interfaces_count: number
  positions: string[]
  status: string
  message: string
}

interface FeatureImportance {
  feature_name: string
  importance: number
  rank: number
}

interface PredictionExplanation {
  prediction_number: string
  position: string
  model_type: string
  explanation: {
    feature_contributions: FeatureImportance[]
    prediction_confidence: number
    top_features: string[]
  }
}

const ShapExplainer: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [shapStatus, setShapStatus] = useState<ShapStatus | null>(null)
  const [predictionNumber, setPredictionNumber] = useState('')
  const [selectedPosition, setSelectedPosition] = useState<string>('hundreds')
  const [selectedModel, setSelectedModel] = useState<string>('xgboost')
  const [explanation, setExplanation] = useState<PredictionExplanation | null>(null)
  const [featureImportance, setFeatureImportance] = useState<FeatureImportance[]>([])
  const [availableModels, setAvailableModels] = useState<string[]>([])

  // 定义选项数组
  const positionOptions = [
    { value: 'hundreds', label: '百位' },
    { value: 'tens', label: '十位' },
    { value: 'units', label: '个位' }
  ]



  // 获取SHAP状态
  const fetchShapStatus = async () => {
    try {
      const response = await apiRequest('/api/shap/status')
      setShapStatus(response.data)
    } catch (error) {
      console.error('获取SHAP状态失败:', error)
      message.error('获取SHAP状态失败')
    }
  }

  // 获取可用模型
  const fetchAvailableModels = async () => {
    try {
      const response = await apiRequest('/api/shap/models/available')
      setAvailableModels(response.data.models || [])
    } catch (error) {
      console.error('获取可用模型失败:', error)
    }
  }

  // 解释单个预测
  const explainPrediction = async () => {
    if (!predictionNumber || predictionNumber.length !== 3) {
      message.error('请输入3位数字的预测号码')
      return
    }

    setLoading(true)
    try {
      const response = await apiRequest('/api/shap/explain/prediction', {
        method: 'POST',
        params: {
          prediction_number: predictionNumber,
          position: selectedPosition,
          model_type: selectedModel
        }
      })
      setExplanation(response.data)
      message.success('预测解释生成成功')
    } catch (error) {
      console.error('预测解释失败:', error)
      message.error('预测解释失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取特征重要性
  const fetchFeatureImportance = async () => {
    setLoading(true)
    try {
      const response = await apiRequest(`/api/shap/explain/features/${selectedPosition}`, {
        params: {
          model_type: selectedModel,
          top_n: 10
        }
      })
      setFeatureImportance(response.data.feature_importance || [])
    } catch (error) {
      console.error('获取特征重要性失败:', error)
      message.error('获取特征重要性失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchShapStatus()
    fetchAvailableModels()
  }, [])

  useEffect(() => {
    if (selectedPosition) {
      fetchFeatureImportance()
    }
  }, [selectedPosition, selectedModel])

  // 特征重要性表格列
  const featureColumns = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 80,
      render: (rank: number) => (
        <Tag color={rank <= 3 ? 'gold' : rank <= 5 ? 'blue' : 'default'}>
          #{rank}
        </Tag>
      )
    },
    {
      title: '特征名称',
      dataIndex: 'feature_name',
      key: 'feature_name',
    },
    {
      title: '重要性',
      dataIndex: 'importance',
      key: 'importance',
      render: (importance: number) => (
        <div>
          <Progress 
            percent={Math.round(importance * 100)} 
            size="small" 
            status={importance > 0.1 ? 'active' : 'normal'}
          />
          <Text type="secondary">{(importance * 100).toFixed(2)}%</Text>
        </div>
      )
    }
  ]

  return (
    <div>
      <Title level={2}>
        <ExperimentOutlined /> SHAP预测解释
      </Title>
      
      {/* SHAP状态卡片 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              {shapStatus?.shap_available ? (
                <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
              ) : (
                <ExclamationCircleOutlined style={{ fontSize: 48, color: '#ff4d4f' }} />
              )}
              <div style={{ marginTop: 8 }}>
                <Text strong style={{ fontSize: '16px' }}>
                  {shapStatus?.shap_available ? '🎯 智能预测系统' : 'SHAP不可用'}
                </Text>
                <br />
                <Text type="secondary">
                  {shapStatus?.shap_available ? '已就绪，为您提供智能分析' : '系统暂时不可用'}
                </Text>
              </div>
            </div>
          </Col>
          <Col span={18}>
            <Row gutter={16}>
              <Col span={12}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <div>
                    <Text strong>🔧 系统状态: </Text>
                    <Tag color={shapStatus?.status === 'available' ? 'green' : 'red'} style={{ fontSize: '12px' }}>
                      {shapStatus?.message || '未知'}
                    </Tag>
                  </div>
                  <div>
                    <Text strong>📍 支持位置: </Text>
                    {shapStatus?.positions?.map(pos => (
                      <Tag key={pos} color="blue" style={{ margin: '2px' }}>
                        {pos === 'hundreds' ? '百位' : pos === 'tens' ? '十位' : '个位'}
                      </Tag>
                    ))}
                  </div>
                  <div>
                    <Text strong>⚙️ 特征接口: </Text>
                    <Text style={{ color: '#1890ff', fontWeight: 'bold' }}>
                      {shapStatus?.feature_interfaces_count || 0} 个
                    </Text>
                  </div>
                </Space>
              </Col>
              <Col span={12}>
                <div style={{ textAlign: 'center', padding: '16px', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', borderRadius: '8px', color: 'white' }}>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong style={{ color: 'white', fontSize: '14px' }}>💡 智能推荐系统</Text>
                  </div>
                  <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: 8 }}>
                    实时动态推荐
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Tag color="rgba(255,255,255,0.2)" style={{ color: 'white', border: '1px solid rgba(255,255,255,0.3)' }}>
                      SHAP分析
                    </Tag>
                    <Tag color="rgba(255,255,255,0.2)" style={{ color: 'white', border: '1px solid rgba(255,255,255,0.3)' }}>
                      机器学习
                    </Tag>
                  </div>
                  <div style={{ marginTop: 8 }}>
                    <Text style={{ fontSize: '12px', color: 'rgba(255,255,255,0.8)' }}>
                      点击"智能推荐"标签查看详细推荐
                    </Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      <Tabs defaultActiveKey="0">
        <TabPane tab={<span><ThunderboltOutlined />智能推荐</span>} key="0">
          <SmartRecommendationCards />
        </TabPane>

        <TabPane tab={<span><HistoryOutlined />学习验证</span>} key="1">
          <Tabs defaultActiveKey="analytics" type="card">
            <TabPane tab={<span><BarChartOutlined />准确率分析</span>} key="analytics">
              <AccuracyAnalytics />
            </TabPane>
            <TabPane tab={<span><SwapOutlined />策略对比</span>} key="comparison">
              <StrategyComparison />
            </TabPane>
            <TabPane tab={<span><HistoryOutlined />预测复盘</span>} key="review">
              <PredictionReview />
            </TabPane>
          </Tabs>
        </TabPane>

        <TabPane tab={<span><BulbOutlined />预测解释</span>} key="2">
          <Card>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Text strong>预测号码:</Text>
                <Input
                  placeholder="输入3位数字"
                  value={predictionNumber}
                  onChange={(e) => setPredictionNumber(e.target.value)}
                  maxLength={3}
                />
              </Col>
              <Col span={6}>
                <Text strong>预测位置:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedPosition}
                  onChange={setSelectedPosition}
                >
                  {positionOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <Text strong>模型类型:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedModel}
                  onChange={setSelectedModel}
                >
                  {availableModels.map(model => (
                    <Option key={model} value={model}>
                      {model.toUpperCase()}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <Button
                  type="primary"
                  icon={<ExperimentOutlined />}
                  onClick={explainPrediction}
                  loading={loading}
                >
                  解释预测
                </Button>
              </Col>
            </Row>

            {explanation && (
              <div style={{ marginTop: 24 }}>
                <Title level={4}>🎯 预测解释结果</Title>

                <Row gutter={16} style={{ marginBottom: 16 }}>
                  <Col span={8}>
                    <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f0f9ff' }}>
                      <div style={{ marginBottom: 8 }}>
                        <Progress
                          type="circle"
                          percent={Math.round(explanation.confidence * 100)}
                          format={percent => `${percent}%`}
                          strokeColor="#1890ff"
                          size={80}
                        />
                      </div>
                      <Text strong style={{ color: '#1890ff' }}>🎯 预测信心度</Text>
                      <div style={{ marginTop: 4 }}>
                        <Tag color={explanation.confidence > 0.7 ? 'green' : explanation.confidence > 0.5 ? 'orange' : 'red'}>
                          {explanation.confidence > 0.7 ? '高置信度' : explanation.confidence > 0.5 ? '中等置信度' : '低置信度'}
                        </Tag>
                      </div>
                    </Card>
                  </Col>

                  <Col span={8}>
                    <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f6ffed' }}>
                      <div style={{ fontSize: '24px', marginBottom: 8 }}>
                        {'⭐'.repeat(Math.max(1, Math.round(explanation.confidence * 5)))}
                      </div>
                      <Text strong style={{ color: '#52c41a' }}>⭐ 推荐强度</Text>
                      <div style={{ marginTop: 4 }}>
                        <Text type="secondary">
                          {Math.max(1, Math.round(explanation.confidence * 5))}/5 星
                        </Text>
                      </div>
                    </Card>
                  </Col>

                  <Col span={8}>
                    <Card size="small" style={{ textAlign: 'center', backgroundColor: '#fff7e6' }}>
                      <div style={{ fontSize: '24px', marginBottom: 8 }}>
                        🛡️
                      </div>
                      <Text strong style={{ color: '#fa8c16' }}>🛡️ 风险评估</Text>
                      <div style={{ marginTop: 4 }}>
                        <Tag color={explanation.confidence > 0.7 ? 'green' : explanation.confidence > 0.5 ? 'orange' : 'volcano'}>
                          {explanation.confidence > 0.7 ? '低风险' : explanation.confidence > 0.5 ? '中等风险' : '高风险'}
                        </Tag>
                        <div style={{ marginTop: 2 }}>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {explanation.confidence > 0.7 ? '建议参考' : explanation.confidence > 0.5 ? '谨慎参考' : '仅供参考'}
                          </Text>
                        </div>
                      </div>
                    </Card>
                  </Col>
                </Row>

                <Card size="small" title="🔍 关键影响因素" style={{ marginTop: 16 }}>
                  {explanation.feature_importance && explanation.feature_importance.length > 0 ? (
                    <div>
                      {explanation.feature_importance.slice(0, 5).map((feature: any, index: number) => (
                        <div key={index} style={{ marginBottom: 8 }}>
                          <Row justify="space-between" align="middle">
                            <Col>
                              <Text strong>#{index + 1} {feature.feature_name}</Text>
                            </Col>
                            <Col>
                              <Text type="secondary">{(feature.importance * 100).toFixed(1)}%</Text>
                            </Col>
                          </Row>
                          <Progress
                            percent={feature.importance * 100}
                            size="small"
                            strokeColor="#52c41a"
                            showInfo={false}
                          />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <Text type="secondary">暂无特征重要性数据</Text>
                  )}
                </Card>
              </div>
            )}
          </Card>
        </TabPane>

        <TabPane tab={<span><BarChartOutlined />特征重要性</span>} key="3">
          <Card>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={8}>
                <Text strong>分析位置:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedPosition}
                  onChange={setSelectedPosition}
                >
                  {positionOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={8}>
                <Text strong>模型类型:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedModel}
                  onChange={setSelectedModel}
                >
                  {availableModels.map(model => (
                    <Option key={model} value={model}>
                      {model.toUpperCase()}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={8}>
                <Button
                  type="primary"
                  icon={<BarChartOutlined />}
                  onClick={fetchFeatureImportance}
                  loading={loading}
                >
                  分析特征重要性
                </Button>
              </Col>
            </Row>

            <Spin spinning={loading}>
              <Table
                columns={featureColumns}
                dataSource={featureImportance}
                rowKey="feature_name"
                pagination={false}
                size="small"
              />
            </Spin>
          </Card>
        </TabPane>

        <TabPane tab={<span><QuestionCircleOutlined />使用指南</span>} key="4">
          <ShapUserGuide />
        </TabPane>
      </Tabs>

    </div>
  )
}

export default ShapExplainer
