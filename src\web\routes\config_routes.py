"""
配置管理API路由
提供功能开关、A/B测试配置管理接口
"""

from fastapi import APIRouter, HTTPException, Query, Body
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel

try:
    from ...config.feature_config import feature_config_manager, FeatureConfig, ABTestConfig, FeatureStatus, ConfigScope
except ImportError:
    from src.config.feature_config import feature_config_manager, FeatureConfig, ABTestConfig, FeatureStatus, ConfigScope

router = APIRouter(prefix="/api/config", tags=["配置管理"])

class FeatureConfigRequest(BaseModel):
    """功能配置请求模型"""
    name: str
    status: str
    scope: str
    value: Any = None
    description: str = ""
    expires_at: Optional[datetime] = None
    conditions: Optional[Dict[str, Any]] = None
    rollout_percentage: float = 0.0
    ab_test_group: Optional[str] = None

class ABTestRequest(BaseModel):
    """A/B测试请求模型"""
    test_name: str
    description: str
    start_date: datetime
    end_date: datetime
    traffic_percentage: float
    groups: Dict[str, Dict[str, Any]]
    success_metrics: List[str] = []

@router.get("/features")
async def get_all_features():
    """获取所有功能配置"""
    try:
        configs = feature_config_manager.get_all_configs()
        
        result = {}
        for name, config in configs.items():
            result[name] = {
                "name": config.name,
                "status": config.status.value,
                "scope": config.scope.value,
                "value": config.value,
                "description": config.description,
                "created_at": config.created_at.isoformat() if config.created_at else None,
                "updated_at": config.updated_at.isoformat() if config.updated_at else None,
                "expires_at": config.expires_at.isoformat() if config.expires_at else None,
                "conditions": config.conditions,
                "rollout_percentage": config.rollout_percentage,
                "ab_test_group": config.ab_test_group
            }
        
        return {
            "status": "success",
            "data": result,
            "count": len(result)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取功能配置失败: {str(e)}")

@router.get("/features/{feature_name}")
async def get_feature_status(
    feature_name: str,
    user_id: Optional[str] = Query(None, description="用户ID"),
    context: Optional[str] = Query(None, description="上下文JSON字符串")
):
    """检查功能状态"""
    try:
        # 解析上下文
        context_dict = None
        if context:
            import json
            context_dict = json.loads(context)
        
        # 检查功能是否启用
        is_enabled = feature_config_manager.is_feature_enabled(
            feature_name, user_id, context_dict
        )
        
        # 获取功能值
        feature_value = feature_config_manager.get_feature_value(
            feature_name, None, user_id, context_dict
        )
        
        return {
            "status": "success",
            "data": {
                "feature_name": feature_name,
                "enabled": is_enabled,
                "value": feature_value,
                "user_id": user_id,
                "context": context_dict
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检查功能状态失败: {str(e)}")

@router.post("/features")
async def create_or_update_feature(request: FeatureConfigRequest):
    """创建或更新功能配置"""
    try:
        config = FeatureConfig(
            name=request.name,
            status=FeatureStatus(request.status),
            scope=ConfigScope(request.scope),
            value=request.value,
            description=request.description,
            expires_at=request.expires_at,
            conditions=request.conditions,
            rollout_percentage=request.rollout_percentage,
            ab_test_group=request.ab_test_group
        )
        
        feature_config_manager.set_feature_config(
            config, 
            changed_by="api", 
            reason="API更新"
        )
        
        return {
            "status": "success",
            "message": f"功能配置 {request.name} 更新成功",
            "data": {
                "name": config.name,
                "status": config.status.value,
                "updated_at": config.updated_at.isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新功能配置失败: {str(e)}")

@router.post("/features/{feature_name}/toggle")
async def toggle_feature(feature_name: str, enabled: bool = Body(..., embed=True)):
    """切换功能开关"""
    try:
        configs = feature_config_manager.get_all_configs()
        config = configs.get(feature_name)
        
        if not config:
            # 创建新配置
            config = FeatureConfig(
                name=feature_name,
                status=FeatureStatus.ENABLED if enabled else FeatureStatus.DISABLED,
                scope=ConfigScope.GLOBAL,
                description=f"通过API创建的功能开关: {feature_name}"
            )
        else:
            # 更新现有配置
            config.status = FeatureStatus.ENABLED if enabled else FeatureStatus.DISABLED
        
        feature_config_manager.set_feature_config(
            config,
            changed_by="api",
            reason=f"功能开关切换: {'启用' if enabled else '禁用'}"
        )
        
        return {
            "status": "success",
            "message": f"功能 {feature_name} 已{'启用' if enabled else '禁用'}",
            "data": {
                "feature_name": feature_name,
                "enabled": enabled
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"切换功能开关失败: {str(e)}")

@router.post("/ab-tests")
async def create_ab_test(request: ABTestRequest):
    """创建A/B测试"""
    try:
        ab_test = ABTestConfig(
            test_name=request.test_name,
            description=request.description,
            start_date=request.start_date,
            end_date=request.end_date,
            traffic_percentage=request.traffic_percentage,
            groups=request.groups,
            success_metrics=request.success_metrics
        )
        
        feature_config_manager.create_ab_test(ab_test)
        
        return {
            "status": "success",
            "message": f"A/B测试 {request.test_name} 创建成功",
            "data": {
                "test_name": ab_test.test_name,
                "start_date": ab_test.start_date.isoformat(),
                "end_date": ab_test.end_date.isoformat(),
                "groups": list(ab_test.groups.keys())
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建A/B测试失败: {str(e)}")

@router.get("/ab-tests/{test_name}/stats")
async def get_ab_test_stats(test_name: str):
    """获取A/B测试统计"""
    try:
        stats = feature_config_manager.get_ab_test_stats(test_name)
        
        return {
            "status": "success",
            "data": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取A/B测试统计失败: {str(e)}")

@router.get("/presets")
async def get_config_presets():
    """获取预设配置"""
    try:
        presets = {
            "智能推荐系统": {
                "smart_recommendation_enabled": {
                    "status": "enabled",
                    "scope": "global",
                    "description": "启用智能推荐功能"
                },
                "recommendation_algorithm_v2": {
                    "status": "testing",
                    "scope": "ab_test",
                    "rollout_percentage": 50.0,
                    "description": "新版推荐算法测试"
                }
            },
            "学习验证平台": {
                "learning_platform_enabled": {
                    "status": "enabled",
                    "scope": "global",
                    "description": "启用学习验证平台"
                },
                "advanced_analytics": {
                    "status": "rollout",
                    "scope": "global",
                    "rollout_percentage": 75.0,
                    "description": "高级分析功能渐进发布"
                }
            },
            "性能监控": {
                "performance_monitoring": {
                    "status": "enabled",
                    "scope": "global",
                    "description": "启用性能监控"
                },
                "detailed_metrics": {
                    "status": "testing",
                    "scope": "ab_test",
                    "description": "详细性能指标收集"
                }
            },
            "用户反馈系统": {
                "feedback_system_enabled": {
                    "status": "disabled",
                    "scope": "global",
                    "description": "用户反馈系统（待开发）"
                },
                "satisfaction_survey": {
                    "status": "disabled",
                    "scope": "global",
                    "description": "满意度调查功能"
                }
            }
        }
        
        return {
            "status": "success",
            "data": presets
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取预设配置失败: {str(e)}")

@router.post("/presets/{preset_name}/apply")
async def apply_preset(preset_name: str):
    """应用预设配置"""
    try:
        presets_response = await get_config_presets()
        presets = presets_response["data"]
        
        if preset_name not in presets:
            raise HTTPException(status_code=404, detail=f"预设配置 {preset_name} 不存在")
        
        preset_configs = presets[preset_name]
        applied_configs = []
        
        for feature_name, config_data in preset_configs.items():
            config = FeatureConfig(
                name=feature_name,
                status=FeatureStatus(config_data["status"]),
                scope=ConfigScope(config_data["scope"]),
                description=config_data["description"],
                rollout_percentage=config_data.get("rollout_percentage", 0.0)
            )
            
            feature_config_manager.set_feature_config(
                config,
                changed_by="api",
                reason=f"应用预设配置: {preset_name}"
            )
            
            applied_configs.append(feature_name)
        
        return {
            "status": "success",
            "message": f"预设配置 {preset_name} 应用成功",
            "data": {
                "preset_name": preset_name,
                "applied_configs": applied_configs,
                "count": len(applied_configs)
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"应用预设配置失败: {str(e)}")

@router.get("/dashboard")
async def get_config_dashboard():
    """获取配置管理仪表板数据"""
    try:
        configs = feature_config_manager.get_all_configs()
        
        # 统计各种状态的配置数量
        status_stats = {}
        scope_stats = {}
        
        for config in configs.values():
            status = config.status.value
            scope = config.scope.value
            
            status_stats[status] = status_stats.get(status, 0) + 1
            scope_stats[scope] = scope_stats.get(scope, 0) + 1
        
        # 获取最近更新的配置
        recent_configs = sorted(
            configs.values(),
            key=lambda c: c.updated_at or datetime.min,
            reverse=True
        )[:10]
        
        recent_list = []
        for config in recent_configs:
            recent_list.append({
                "name": config.name,
                "status": config.status.value,
                "updated_at": config.updated_at.isoformat() if config.updated_at else None,
                "description": config.description
            })
        
        return {
            "status": "success",
            "data": {
                "total_configs": len(configs),
                "status_distribution": status_stats,
                "scope_distribution": scope_stats,
                "recent_updates": recent_list
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置仪表板失败: {str(e)}")

@router.delete("/features/{feature_name}")
async def delete_feature(feature_name: str):
    """删除功能配置"""
    try:
        configs = feature_config_manager.get_all_configs()
        if feature_name not in configs:
            raise HTTPException(status_code=404, detail=f"功能配置 {feature_name} 不存在")
        
        # 设置为禁用状态而不是真正删除
        config = configs[feature_name]
        config.status = FeatureStatus.DISABLED
        config.description += " [已删除]"
        
        feature_config_manager.set_feature_config(
            config,
            changed_by="api",
            reason="通过API删除"
        )
        
        return {
            "status": "success",
            "message": f"功能配置 {feature_name} 已删除"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除功能配置失败: {str(e)}")
