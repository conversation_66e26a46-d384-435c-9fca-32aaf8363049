import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Switch,
  Timeline,
  Progress,
  Tag,
  Space,
  Typography,
  Divider,
  <PERSON><PERSON>,
  List,
  Badge,
  Tooltip,
  Modal,
  message,
  Spin
} from 'antd';
import {
  RobotOutlined,
  <PERSON>boltOutlined,
  SettingOutlined,
  Check<PERSON>ircleOutlined,
  <PERSON><PERSON>ircleOutlined,
  Exclamation<PERSON>ircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  BulbOutlined,
  TrophyOutlined,
  FireOutlined,
  EyeOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Title, Text, Paragraph } = Typography;

interface OptimizationStatus {
  engine_status: string;
  total_executions: number;
  recent_executions: Array<{
    action_id: string;
    execution_time: string;
    success: boolean;
    notes: string;
  }>;
  optimization_rules_count: number;
  last_check: string;
}

interface SchedulerStatus {
  is_running: boolean;
  optimization_interval: number;
  last_optimization_time: string | null;
  optimization_count: number;
  next_optimization_time: string;
  auto_optimization_enabled: boolean;
}

interface OptimizationAction {
  action_id: string;
  action_type: string;
  target: string;
  description: string;
  priority: number;
  impact_score: number;
  auto_executable: boolean;
  parameters: any;
}

const AutoOptimizationDashboard: React.FC = () => {
  const [optimizationStatus, setOptimizationStatus] = useState<OptimizationStatus | null>(null);
  const [schedulerStatus, setSchedulerStatus] = useState<SchedulerStatus | null>(null);
  const [pendingActions, setPendingActions] = useState<OptimizationAction[]>([]);
  const [loading, setLoading] = useState(false);
  const [autoOptEnabled, setAutoOptEnabled] = useState(true);
  const [showAnalysisModal, setShowAnalysisModal] = useState(false);

  useEffect(() => {
    loadOptimizationData();
    // 每30秒刷新一次数据
    const interval = setInterval(loadOptimizationData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadOptimizationData = async () => {
    try {
      const [statusRes, schedulerRes] = await Promise.all([
        axios.get('/api/feedback/auto-optimize/status'),
        axios.get('/api/feedback/auto-optimize/scheduler/status')
      ]);

      setOptimizationStatus(statusRes.data.data);
      setSchedulerStatus(schedulerRes.data.data);
      setAutoOptEnabled(schedulerRes.data.data.is_running);
    } catch (error) {
      console.error('加载自动优化数据失败:', error);
    }
  };

  const handleToggleAutoOptimization = async (enabled: boolean) => {
    setLoading(true);
    try {
      const endpoint = enabled ? 'start' : 'stop';
      await axios.post(`/api/feedback/auto-optimize/scheduler/${endpoint}`);
      
      setAutoOptEnabled(enabled);
      message.success(`自动优化已${enabled ? '启用' : '停用'}`);
      
      // 刷新状态
      setTimeout(loadOptimizationData, 1000);
    } catch (error) {
      console.error('切换自动优化失败:', error);
      message.error('操作失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleTriggerOptimization = async () => {
    setLoading(true);
    try {
      const response = await axios.post('/api/feedback/auto-optimize/trigger');
      message.success('优化已触发，正在执行...');
      
      // 刷新状态
      setTimeout(loadOptimizationData, 2000);
    } catch (error) {
      console.error('触发优化失败:', error);
      message.error('触发失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleAnalyzeFeedback = async () => {
    setLoading(true);
    try {
      const response = await axios.post('/api/feedback/auto-optimize/analyze');
      setPendingActions(response.data.data.optimization_actions || []);
      setShowAnalysisModal(true);
      message.success(`分析完成，发现 ${response.data.data.total_actions} 个优化建议`);
    } catch (error) {
      console.error('分析反馈失败:', error);
      message.error('分析失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'red';
      default: return 'orange';
    }
  };

  const getPriorityColor = (priority: number) => {
    if (priority >= 4) return 'red';
    if (priority >= 3) return 'orange';
    return 'blue';
  };

  const formatTime = (timeStr: string | null) => {
    if (!timeStr) return '未执行';
    if (timeStr === '立即') return '立即';
    return new Date(timeStr).toLocaleString();
  };

  const renderOptimizationOverview = () => (
    <Row gutter={[16, 16]}>
      <Col span={6}>
        <Card>
          <Statistic
            title="自动优化引擎"
            value={optimizationStatus?.engine_status || 'unknown'}
            prefix={<RobotOutlined />}
            valueStyle={{ color: getStatusColor(optimizationStatus?.engine_status || '') }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="总执行次数"
            value={optimizationStatus?.total_executions || 0}
            prefix={<ThunderboltOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="优化规则数"
            value={optimizationStatus?.optimization_rules_count || 0}
            prefix={<SettingOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="调度器状态"
            value={schedulerStatus?.is_running ? '运行中' : '已停止'}
            prefix={schedulerStatus?.is_running ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
            valueStyle={{ color: schedulerStatus?.is_running ? 'green' : 'red' }}
          />
        </Card>
      </Col>
    </Row>
  );

  const renderControlPanel = () => (
    <Card title="🎛️ 自动优化控制面板" style={{ marginTop: 16 }}>
      <Row gutter={[16, 16]} align="middle">
        <Col span={8}>
          <Space direction="vertical">
            <Text strong>自动优化开关</Text>
            <Switch
              checked={autoOptEnabled}
              onChange={handleToggleAutoOptimization}
              loading={loading}
              checkedChildren="启用"
              unCheckedChildren="停用"
            />
            <Text type="secondary">
              {autoOptEnabled ? '系统将自动根据用户反馈进行优化' : '自动优化已停用'}
            </Text>
          </Space>
        </Col>
        <Col span={8}>
          <Space direction="vertical">
            <Text strong>立即触发优化</Text>
            <Button
              type="primary"
              icon={<FireOutlined />}
              onClick={handleTriggerOptimization}
              loading={loading}
            >
              立即优化
            </Button>
            <Text type="secondary">手动触发一次完整的优化周期</Text>
          </Space>
        </Col>
        <Col span={8}>
          <Space direction="vertical">
            <Text strong>分析用户反馈</Text>
            <Button
              icon={<EyeOutlined />}
              onClick={handleAnalyzeFeedback}
              loading={loading}
            >
              分析反馈
            </Button>
            <Text type="secondary">分析当前反馈并生成优化建议</Text>
          </Space>
        </Col>
      </Row>
    </Card>
  );

  const renderSchedulerInfo = () => (
    <Card title="⏰ 调度器信息" style={{ marginTop: 16 }}>
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>优化间隔</Text>
            <Text>{Math.floor((schedulerStatus?.optimization_interval || 0) / 60)} 分钟</Text>
            
            <Text strong>上次优化时间</Text>
            <Text>{formatTime(schedulerStatus?.last_optimization_time)}</Text>
            
            <Text strong>下次优化时间</Text>
            <Text>{formatTime(schedulerStatus?.next_optimization_time)}</Text>
          </Space>
        </Col>
        <Col span={12}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>已执行优化次数</Text>
            <Text>{schedulerStatus?.optimization_count || 0} 次</Text>
            
            <Text strong>自动优化状态</Text>
            <Badge 
              status={schedulerStatus?.auto_optimization_enabled ? 'processing' : 'default'}
              text={schedulerStatus?.auto_optimization_enabled ? '已启用' : '已禁用'}
            />
          </Space>
        </Col>
      </Row>
    </Card>
  );

  const renderRecentExecutions = () => (
    <Card title="📋 最近执行记录" style={{ marginTop: 16 }}>
      {optimizationStatus?.recent_executions?.length ? (
        <Timeline>
          {optimizationStatus.recent_executions.map((execution, index) => (
            <Timeline.Item
              key={execution.action_id}
              color={execution.success ? 'green' : 'red'}
              dot={execution.success ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
            >
              <Space direction="vertical" size="small">
                <Text strong>{execution.success ? '执行成功' : '执行失败'}</Text>
                <Text type="secondary">{execution.notes}</Text>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {new Date(execution.execution_time).toLocaleString()}
                </Text>
              </Space>
            </Timeline.Item>
          ))}
        </Timeline>
      ) : (
        <Text type="secondary">暂无执行记录</Text>
      )}
    </Card>
  );

  const renderOptimizationRules = () => (
    <Card title="🧠 优化规则库" style={{ marginTop: 16 }}>
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card size="small">
            <Space direction="vertical" align="center" style={{ width: '100%' }}>
              <TrophyOutlined style={{ fontSize: 24, color: '#1890ff' }} />
              <Text strong>预测准确率优化</Text>
              <Text type="secondary" style={{ textAlign: 'center' }}>
                基于准确率反馈自动调整算法参数
              </Text>
            </Space>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Space direction="vertical" align="center" style={{ width: '100%' }}>
              <BulbOutlined style={{ fontSize: 24, color: '#52c41a' }} />
              <Text strong>界面优化</Text>
              <Text type="secondary" style={{ textAlign: 'center' }}>
                根据易用性反馈简化操作流程
              </Text>
            </Space>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Space direction="vertical" align="center" style={{ width: '100%' }}>
              <SettingOutlined style={{ fontSize: 24, color: '#faad14' }} />
              <Text strong>功能完整性</Text>
              <Text type="secondary" style={{ textAlign: 'center' }}>
                基于功能需求自动启用新特性
              </Text>
            </Space>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Space direction="vertical" align="center" style={{ width: '100%' }}>
              <RobotOutlined style={{ fontSize: 24, color: '#eb2f96' }} />
              <Text strong>自动优化</Text>
              <Text type="secondary" style={{ textAlign: 'center' }}>
                持续改进和自我优化机制
              </Text>
            </Space>
          </Card>
        </Col>
      </Row>
    </Card>
  );

  return (
    <div style={{ padding: '20px' }}>
      <Title level={2}>
        <RobotOutlined style={{ marginRight: 8, color: '#1890ff' }} />
        自动优化系统
      </Title>
      <Paragraph type="secondary">
        基于用户反馈的智能自动优化系统，实现系统的持续改进和迭代。
      </Paragraph>

      <Alert
        message="🤖 智能自动优化"
        description="系统会自动分析用户反馈，识别问题和改进机会，并自动执行优化措施，无需人工干预。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {renderOptimizationOverview()}
      {renderControlPanel()}
      {renderSchedulerInfo()}
      {renderOptimizationRules()}
      {renderRecentExecutions()}

      <Modal
        title="📊 反馈分析结果"
        open={showAnalysisModal}
        onCancel={() => setShowAnalysisModal(false)}
        footer={[
          <Button key="close" onClick={() => setShowAnalysisModal(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <List
          dataSource={pendingActions}
          renderItem={(action) => (
            <List.Item>
              <List.Item.Meta
                title={
                  <Space>
                    <Text strong>{action.description}</Text>
                    <Tag color={getPriorityColor(action.priority)}>
                      优先级 {action.priority}
                    </Tag>
                    {action.auto_executable && (
                      <Tag color="green">可自动执行</Tag>
                    )}
                  </Space>
                }
                description={
                  <Space direction="vertical" size="small">
                    <Text type="secondary">类型: {action.action_type}</Text>
                    <Text type="secondary">目标: {action.target}</Text>
                    <Text type="secondary">影响评分: {action.impact_score}</Text>
                  </Space>
                }
              />
            </List.Item>
          )}
          locale={{ emptyText: '暂无优化建议' }}
        />
      </Modal>
    </div>
  );
};

export default AutoOptimizationDashboard;
