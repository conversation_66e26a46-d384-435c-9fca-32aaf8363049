"""
性能监控系统
实现API响应时间跟踪、用户行为分析、错误监控和报警
"""

import time
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import json
import sqlite3
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class APIMetrics:
    """API性能指标"""
    endpoint: str
    method: str
    response_time: float
    status_code: int
    timestamp: datetime
    user_id: Optional[str] = None
    error_message: Optional[str] = None

@dataclass
class UserBehavior:
    """用户行为数据"""
    user_id: str
    action: str
    page: str
    timestamp: datetime
    session_id: str
    additional_data: Optional[Dict] = None

@dataclass
class SystemMetrics:
    """系统性能指标"""
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    active_connections: int
    timestamp: datetime

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, db_path: str = "data/performance_monitor.db"):
        self.db_path = db_path
        self.api_metrics: deque = deque(maxlen=10000)  # 最近10000条API记录
        self.user_behaviors: deque = deque(maxlen=5000)  # 最近5000条用户行为
        self.system_metrics: deque = deque(maxlen=1000)  # 最近1000条系统指标
        self.error_counts = defaultdict(int)
        self.response_time_cache = defaultdict(list)
        
        # 初始化数据库
        self._init_database()
        
        # 启动后台监控任务
        self._start_background_tasks()
    
    def _init_database(self):
        """初始化数据库表"""
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            # API性能表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS api_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    endpoint TEXT NOT NULL,
                    method TEXT NOT NULL,
                    response_time REAL NOT NULL,
                    status_code INTEGER NOT NULL,
                    timestamp DATETIME NOT NULL,
                    user_id TEXT,
                    error_message TEXT
                )
            """)
            
            # 用户行为表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_behaviors (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    action TEXT NOT NULL,
                    page TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    session_id TEXT NOT NULL,
                    additional_data TEXT
                )
            """)
            
            # 系统性能表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS system_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cpu_usage REAL NOT NULL,
                    memory_usage REAL NOT NULL,
                    disk_usage REAL NOT NULL,
                    active_connections INTEGER NOT NULL,
                    timestamp DATETIME NOT NULL
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_api_timestamp ON api_metrics(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_user_timestamp ON user_behaviors(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_system_timestamp ON system_metrics(timestamp)")
    
    def track_api_request(self, endpoint: str, method: str, response_time: float, 
                         status_code: int, user_id: Optional[str] = None, 
                         error_message: Optional[str] = None):
        """跟踪API请求性能"""
        metric = APIMetrics(
            endpoint=endpoint,
            method=method,
            response_time=response_time,
            status_code=status_code,
            timestamp=datetime.now(),
            user_id=user_id,
            error_message=error_message
        )
        
        # 添加到内存缓存
        self.api_metrics.append(metric)
        
        # 更新响应时间缓存
        self.response_time_cache[endpoint].append(response_time)
        if len(self.response_time_cache[endpoint]) > 100:
            self.response_time_cache[endpoint].pop(0)
        
        # 错误计数
        if status_code >= 400:
            self.error_counts[f"{endpoint}_{status_code}"] += 1
        
        # 异步保存到数据库
        asyncio.create_task(self._save_api_metric(metric))
        
        # 检查是否需要报警
        self._check_api_alerts(metric)
    
    def track_user_behavior(self, user_id: str, action: str, page: str, 
                           session_id: str, additional_data: Optional[Dict] = None):
        """跟踪用户行为"""
        behavior = UserBehavior(
            user_id=user_id,
            action=action,
            page=page,
            timestamp=datetime.now(),
            session_id=session_id,
            additional_data=additional_data
        )
        
        # 添加到内存缓存
        self.user_behaviors.append(behavior)
        
        # 异步保存到数据库
        asyncio.create_task(self._save_user_behavior(behavior))
    
    def track_system_metrics(self, cpu_usage: float, memory_usage: float, 
                           disk_usage: float, active_connections: int):
        """跟踪系统性能指标"""
        metric = SystemMetrics(
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            disk_usage=disk_usage,
            active_connections=active_connections,
            timestamp=datetime.now()
        )
        
        # 添加到内存缓存
        self.system_metrics.append(metric)
        
        # 异步保存到数据库
        asyncio.create_task(self._save_system_metric(metric))
        
        # 检查系统资源报警
        self._check_system_alerts(metric)
    
    async def _save_api_metric(self, metric: APIMetrics):
        """保存API指标到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO api_metrics 
                    (endpoint, method, response_time, status_code, timestamp, user_id, error_message)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    metric.endpoint, metric.method, metric.response_time,
                    metric.status_code, metric.timestamp, metric.user_id, metric.error_message
                ))
        except Exception as e:
            logger.error(f"保存API指标失败: {e}")
    
    async def _save_user_behavior(self, behavior: UserBehavior):
        """保存用户行为到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                additional_data_json = json.dumps(behavior.additional_data) if behavior.additional_data else None
                conn.execute("""
                    INSERT INTO user_behaviors 
                    (user_id, action, page, timestamp, session_id, additional_data)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    behavior.user_id, behavior.action, behavior.page,
                    behavior.timestamp, behavior.session_id, additional_data_json
                ))
        except Exception as e:
            logger.error(f"保存用户行为失败: {e}")
    
    async def _save_system_metric(self, metric: SystemMetrics):
        """保存系统指标到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO system_metrics 
                    (cpu_usage, memory_usage, disk_usage, active_connections, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    metric.cpu_usage, metric.memory_usage, metric.disk_usage,
                    metric.active_connections, metric.timestamp
                ))
        except Exception as e:
            logger.error(f"保存系统指标失败: {e}")
    
    def _check_api_alerts(self, metric: APIMetrics):
        """检查API性能报警"""
        # 响应时间过长报警
        if metric.response_time > 5.0:  # 5秒
            logger.warning(f"API响应时间过长: {metric.endpoint} - {metric.response_time:.2f}s")
        
        # 错误率过高报警
        if metric.status_code >= 500:
            logger.error(f"服务器错误: {metric.endpoint} - {metric.status_code}")
    
    def _check_system_alerts(self, metric: SystemMetrics):
        """检查系统资源报警"""
        if metric.cpu_usage > 80:
            logger.warning(f"CPU使用率过高: {metric.cpu_usage:.1f}%")
        
        if metric.memory_usage > 85:
            logger.warning(f"内存使用率过高: {metric.memory_usage:.1f}%")
        
        if metric.disk_usage > 90:
            logger.warning(f"磁盘使用率过高: {metric.disk_usage:.1f}%")
    
    def _start_background_tasks(self):
        """启动后台监控任务"""
        # 这里可以启动定期清理、数据聚合等后台任务
        pass
    
    def get_api_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取API性能摘要"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [m for m in self.api_metrics if m.timestamp > cutoff_time]
        
        if not recent_metrics:
            return {"message": "暂无数据"}
        
        # 计算统计信息
        total_requests = len(recent_metrics)
        avg_response_time = sum(m.response_time for m in recent_metrics) / total_requests
        error_count = sum(1 for m in recent_metrics if m.status_code >= 400)
        error_rate = error_count / total_requests * 100
        
        # 按端点分组统计
        endpoint_stats = defaultdict(list)
        for metric in recent_metrics:
            endpoint_stats[metric.endpoint].append(metric.response_time)
        
        endpoint_summary = {}
        for endpoint, times in endpoint_stats.items():
            endpoint_summary[endpoint] = {
                "avg_response_time": sum(times) / len(times),
                "max_response_time": max(times),
                "min_response_time": min(times),
                "request_count": len(times)
            }
        
        return {
            "period_hours": hours,
            "total_requests": total_requests,
            "avg_response_time": round(avg_response_time, 3),
            "error_count": error_count,
            "error_rate": round(error_rate, 2),
            "endpoint_summary": endpoint_summary
        }

# 全局性能监控实例
performance_monitor = PerformanceMonitor()
