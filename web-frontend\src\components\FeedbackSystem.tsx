import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Rate,
  Select,
  Radio,
  message,
  Tabs,
  Space,
  Typography,
  Divider,
  Tag,
  Modal,
  List,
  Avatar,
  Tooltip
} from 'antd';
import {
  MessageOutlined,
  StarOutlined,
  BulbOutlined,
  BugOutlined,
  SendOutlined,
  HistoryOutlined,
  ThumbsUpOutlined,
  ThumbsDownOutlined,
  HeartOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;

interface FeedbackFormData {
  feedback_type: string;
  content: string;
  rating?: number;
  prediction_period?: string;
  prediction_numbers?: string;
  actual_numbers?: string;
  tags?: string[];
}

interface UserFeedback {
  feedback_id: string;
  feedback_type: string;
  content: string;
  rating?: number;
  created_at: string;
  status: string;
}

const FeedbackSystem: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [feedbackHistory, setFeedbackHistory] = useState<UserFeedback[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [quickFeedbackVisible, setQuickFeedbackVisible] = useState(false);

  // 预定义的反馈标签
  const feedbackTags = {
    prediction: ['准确', '不准确', '有用', '需要改进'],
    feature: ['易用', '复杂', '缺少功能', '界面友好'],
    suggestion: ['新功能', '性能优化', '界面改进', '数据展示'],
    bug: ['页面错误', '数据错误', '功能异常', '性能问题']
  };

  // 反馈类型配置
  const feedbackTypes = [
    { value: 'prediction', label: '预测反馈', icon: <StarOutlined />, color: 'blue' },
    { value: 'feature', label: '功能反馈', icon: <BulbOutlined />, color: 'green' },
    { value: 'suggestion', label: '改进建议', icon: <MessageOutlined />, color: 'orange' },
    { value: 'bug', label: '问题报告', icon: <BugOutlined />, color: 'red' }
  ];

  useEffect(() => {
    loadFeedbackHistory();
  }, []);

  const loadFeedbackHistory = async () => {
    try {
      const response = await axios.get('/api/feedback/user-feedback', {
        params: { limit: 10 }
      });
      setFeedbackHistory(response.data.data || []);
    } catch (error) {
      console.error('加载反馈历史失败:', error);
    }
  };

  const handleSubmitFeedback = async (values: FeedbackFormData) => {
    setLoading(true);
    try {
      const feedbackData = {
        ...values,
        user_id: 'current_user', // 实际项目中从用户上下文获取
        tags: values.tags || []
      };

      await axios.post('/api/feedback/submit', feedbackData);
      
      message.success('反馈提交成功，感谢您的宝贵意见！');
      form.resetFields();
      loadFeedbackHistory();
    } catch (error) {
      console.error('提交反馈失败:', error);
      message.error('提交失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickFeedback = async (type: 'positive' | 'negative', content: string) => {
    try {
      await axios.post('/api/feedback/quick', {
        user_id: 'current_user',
        feedback_type: 'feature',
        content: content,
        rating: type === 'positive' ? 5 : 2
      });
      
      message.success('快速反馈已提交！');
      setQuickFeedbackVisible(false);
      loadFeedbackHistory();
    } catch (error) {
      console.error('快速反馈失败:', error);
      message.error('提交失败，请稍后重试');
    }
  };

  const renderFeedbackForm = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmitFeedback}
      initialValues={{ feedback_type: 'feature' }}
    >
      <Form.Item
        name="feedback_type"
        label="反馈类型"
        rules={[{ required: true, message: '请选择反馈类型' }]}
      >
        <Radio.Group>
          {feedbackTypes.map(type => (
            <Radio.Button key={type.value} value={type.value}>
              <Space>
                {type.icon}
                {type.label}
              </Space>
            </Radio.Button>
          ))}
        </Radio.Group>
      </Form.Item>

      <Form.Item
        name="content"
        label="反馈内容"
        rules={[
          { required: true, message: '请输入反馈内容' },
          { min: 10, message: '反馈内容至少10个字符' }
        ]}
      >
        <TextArea
          rows={4}
          placeholder="请详细描述您的反馈，这将帮助我们更好地改进系统..."
          showCount
          maxLength={500}
        />
      </Form.Item>

      <Form.Item
        name="rating"
        label="满意度评分"
      >
        <Rate
          allowHalf
          character={<HeartOutlined />}
          style={{ color: '#ff6b6b' }}
        />
      </Form.Item>

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) => 
          prevValues.feedback_type !== currentValues.feedback_type
        }
      >
        {({ getFieldValue }) => {
          const feedbackType = getFieldValue('feedback_type');
          if (feedbackType === 'prediction') {
            return (
              <>
                <Form.Item
                  name="prediction_period"
                  label="相关期号"
                >
                  <Input placeholder="如：2025214" />
                </Form.Item>
                <Form.Item
                  name="prediction_numbers"
                  label="预测号码"
                >
                  <Input placeholder="如：407" />
                </Form.Item>
                <Form.Item
                  name="actual_numbers"
                  label="实际开奖号码"
                >
                  <Input placeholder="如：123" />
                </Form.Item>
              </>
            );
          }
          return null;
        }}
      </Form.Item>

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) => 
          prevValues.feedback_type !== currentValues.feedback_type
        }
      >
        {({ getFieldValue }) => {
          const feedbackType = getFieldValue('feedback_type');
          const tags = feedbackTags[feedbackType as keyof typeof feedbackTags] || [];
          
          if (tags.length > 0) {
            return (
              <Form.Item
                name="tags"
                label="相关标签"
              >
                <Select
                  mode="multiple"
                  placeholder="选择相关标签（可选）"
                  allowClear
                >
                  {tags.map(tag => (
                    <Option key={tag} value={tag}>{tag}</Option>
                  ))}
                </Select>
              </Form.Item>
            );
          }
          return null;
        }}
      </Form.Item>

      <Form.Item>
        <Space>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={<SendOutlined />}
          >
            提交反馈
          </Button>
          <Button onClick={() => form.resetFields()}>
            重置
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );

  const renderQuickFeedback = () => (
    <Card size="small" style={{ marginBottom: 16 }}>
      <div style={{ textAlign: 'center' }}>
        <Text strong>快速反馈</Text>
        <br />
        <Text type="secondary">对当前功能的快速评价</Text>
        <br />
        <Space style={{ marginTop: 8 }}>
          <Tooltip title="功能很好用">
            <Button
              type="text"
              icon={<ThumbsUpOutlined />}
              onClick={() => handleQuickFeedback('positive', '功能很好用')}
            >
              👍 好用
            </Button>
          </Tooltip>
          <Tooltip title="需要改进">
            <Button
              type="text"
              icon={<ThumbsDownOutlined />}
              onClick={() => handleQuickFeedback('negative', '需要改进')}
            >
              👎 需要改进
            </Button>
          </Tooltip>
        </Space>
      </div>
    </Card>
  );

  const renderFeedbackHistory = () => (
    <List
      dataSource={feedbackHistory}
      renderItem={(item) => (
        <List.Item>
          <List.Item.Meta
            avatar={
              <Avatar
                icon={
                  feedbackTypes.find(t => t.value === item.feedback_type)?.icon || <MessageOutlined />
                }
                style={{
                  backgroundColor: feedbackTypes.find(t => t.value === item.feedback_type)?.color || 'blue'
                }}
              />
            }
            title={
              <Space>
                <Text strong>
                  {feedbackTypes.find(t => t.value === item.feedback_type)?.label || '反馈'}
                </Text>
                {item.rating && (
                  <Rate
                    disabled
                    value={item.rating}
                    style={{ fontSize: 12 }}
                  />
                )}
                <Tag color={item.status === 'active' ? 'green' : 'default'}>
                  {item.status === 'active' ? '处理中' : '已处理'}
                </Tag>
              </Space>
            }
            description={
              <div>
                <Paragraph ellipsis={{ rows: 2 }}>
                  {item.content}
                </Paragraph>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {new Date(item.created_at).toLocaleString()}
                </Text>
              </div>
            }
          />
        </List.Item>
      )}
      locale={{ emptyText: '暂无反馈记录' }}
    />
  );

  return (
    <div style={{ maxWidth: 800, margin: '0 auto', padding: '20px' }}>
      <Card>
        <Title level={3}>
          <MessageOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          用户反馈系统
        </Title>
        <Paragraph type="secondary">
          您的反馈对我们非常重要，帮助我们不断改进福彩3D预测系统的功能和体验。
        </Paragraph>

        <Tabs defaultActiveKey="feedback">
          <TabPane
            tab={
              <span>
                <MessageOutlined />
                提交反馈
              </span>
            }
            key="feedback"
          >
            {renderQuickFeedback()}
            <Divider />
            {renderFeedbackForm()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <HistoryOutlined />
                反馈历史
              </span>
            }
            key="history"
          >
            <div style={{ marginBottom: 16 }}>
              <Button
                onClick={loadFeedbackHistory}
                icon={<HistoryOutlined />}
              >
                刷新
              </Button>
            </div>
            {renderFeedbackHistory()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 反馈提示卡片 */}
      <Card size="small" style={{ marginTop: 16, background: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <Space>
          <BulbOutlined style={{ color: '#52c41a' }} />
          <Text>
            <strong>提示：</strong>
            详细的反馈能帮助我们更好地理解您的需求，提供更准确的预测服务。
          </Text>
        </Space>
      </Card>
    </div>
  );
};

export default FeedbackSystem;
