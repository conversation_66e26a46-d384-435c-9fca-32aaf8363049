"""
用户反馈数据管理器
创建用户反馈表、用户偏好表等，支持反馈收集和个性化优化功能
"""

import sqlite3
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import uuid

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class UserFeedback:
    """用户反馈数据模型"""
    feedback_id: str
    user_id: str
    feedback_type: str  # prediction, feature, suggestion, bug
    content: str
    rating: Optional[int] = None  # 1-5星评分
    prediction_period: Optional[str] = None  # 相关期号
    prediction_numbers: Optional[str] = None  # 预测号码
    actual_numbers: Optional[str] = None  # 实际开奖号码
    accuracy: Optional[float] = None  # 准确率
    tags: Optional[List[str]] = None  # 标签
    metadata: Optional[Dict[str, Any]] = None  # 额外元数据
    created_at: datetime = None
    updated_at: datetime = None
    status: str = "active"  # active, resolved, archived

@dataclass
class SatisfactionSurvey:
    """满意度调查数据模型"""
    survey_id: str
    user_id: str
    overall_satisfaction: int  # 1-10 NPS评分
    prediction_accuracy_rating: int  # 1-5星
    interface_usability_rating: int  # 1-5星
    feature_completeness_rating: int  # 1-5星
    recommendation_quality_rating: int  # 1-5星
    likelihood_to_recommend: int  # 1-10 NPS
    comments: Optional[str] = None
    survey_version: str = "v1.0"
    created_at: datetime = None

@dataclass
class UserPreference:
    """用户偏好数据模型"""
    user_id: str
    preference_type: str  # prediction_style, notification, display
    preference_key: str
    preference_value: Any
    confidence_score: float = 0.0  # 偏好置信度
    source: str = "explicit"  # explicit, implicit, inferred
    created_at: datetime = None
    updated_at: datetime = None

class FeedbackDataManager:
    """用户反馈数据管理器"""
    
    def __init__(self, db_path: str = "data/feedback.db"):
        self.db_path = db_path
        self._init_database()
        logger.info("用户反馈数据管理器初始化完成")
    
    def _init_database(self):
        """初始化数据库表"""
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            # 用户反馈表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_feedback (
                    feedback_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    feedback_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    rating INTEGER,
                    prediction_period TEXT,
                    prediction_numbers TEXT,
                    actual_numbers TEXT,
                    accuracy REAL,
                    tags TEXT,
                    metadata TEXT,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL,
                    status TEXT DEFAULT 'active'
                )
            """)
            
            # 满意度调查表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS satisfaction_surveys (
                    survey_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    overall_satisfaction INTEGER NOT NULL,
                    prediction_accuracy_rating INTEGER NOT NULL,
                    interface_usability_rating INTEGER NOT NULL,
                    feature_completeness_rating INTEGER NOT NULL,
                    recommendation_quality_rating INTEGER NOT NULL,
                    likelihood_to_recommend INTEGER NOT NULL,
                    comments TEXT,
                    survey_version TEXT DEFAULT 'v1.0',
                    created_at DATETIME NOT NULL
                )
            """)
            
            # 用户偏好表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_preferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    preference_type TEXT NOT NULL,
                    preference_key TEXT NOT NULL,
                    preference_value TEXT NOT NULL,
                    confidence_score REAL DEFAULT 0.0,
                    source TEXT DEFAULT 'explicit',
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL,
                    UNIQUE(user_id, preference_type, preference_key)
                )
            """)
            
            # 反馈统计表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS feedback_statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stat_date DATE NOT NULL,
                    feedback_count INTEGER DEFAULT 0,
                    avg_rating REAL DEFAULT 0.0,
                    satisfaction_score REAL DEFAULT 0.0,
                    nps_score REAL DEFAULT 0.0,
                    top_issues TEXT,
                    created_at DATETIME NOT NULL,
                    UNIQUE(stat_date)
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_feedback_user_id ON user_feedback(user_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_feedback_type ON user_feedback(feedback_type)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_feedback_created ON user_feedback(created_at)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_survey_user_id ON satisfaction_surveys(user_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_preferences_user ON user_preferences(user_id)")
    
    def create_feedback(self, feedback: UserFeedback) -> str:
        """创建用户反馈"""
        if not feedback.feedback_id:
            feedback.feedback_id = str(uuid.uuid4())
        
        if not feedback.created_at:
            feedback.created_at = datetime.now()
        feedback.updated_at = datetime.now()
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO user_feedback 
                (feedback_id, user_id, feedback_type, content, rating, prediction_period, 
                 prediction_numbers, actual_numbers, accuracy, tags, metadata, 
                 created_at, updated_at, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                feedback.feedback_id,
                feedback.user_id,
                feedback.feedback_type,
                feedback.content,
                feedback.rating,
                feedback.prediction_period,
                feedback.prediction_numbers,
                feedback.actual_numbers,
                feedback.accuracy,
                json.dumps(feedback.tags) if feedback.tags else None,
                json.dumps(feedback.metadata) if feedback.metadata else None,
                feedback.created_at,
                feedback.updated_at,
                feedback.status
            ))
        
        logger.info(f"创建用户反馈: {feedback.feedback_id}")
        return feedback.feedback_id
    
    def create_satisfaction_survey(self, survey: SatisfactionSurvey) -> str:
        """创建满意度调查"""
        if not survey.survey_id:
            survey.survey_id = str(uuid.uuid4())
        
        if not survey.created_at:
            survey.created_at = datetime.now()
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO satisfaction_surveys 
                (survey_id, user_id, overall_satisfaction, prediction_accuracy_rating,
                 interface_usability_rating, feature_completeness_rating, 
                 recommendation_quality_rating, likelihood_to_recommend, 
                 comments, survey_version, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                survey.survey_id,
                survey.user_id,
                survey.overall_satisfaction,
                survey.prediction_accuracy_rating,
                survey.interface_usability_rating,
                survey.feature_completeness_rating,
                survey.recommendation_quality_rating,
                survey.likelihood_to_recommend,
                survey.comments,
                survey.survey_version,
                survey.created_at
            ))
        
        logger.info(f"创建满意度调查: {survey.survey_id}")
        return survey.survey_id
    
    def set_user_preference(self, preference: UserPreference):
        """设置用户偏好"""
        if not preference.created_at:
            preference.created_at = datetime.now()
        preference.updated_at = datetime.now()
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO user_preferences 
                (user_id, preference_type, preference_key, preference_value, 
                 confidence_score, source, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                preference.user_id,
                preference.preference_type,
                preference.preference_key,
                json.dumps(preference.preference_value),
                preference.confidence_score,
                preference.source,
                preference.created_at,
                preference.updated_at
            ))
        
        logger.info(f"设置用户偏好: {preference.user_id} - {preference.preference_key}")
    
    def get_user_feedback(self, user_id: str = None, feedback_type: str = None, 
                         limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户反馈"""
        query = "SELECT * FROM user_feedback WHERE 1=1"
        params = []
        
        if user_id:
            query += " AND user_id = ?"
            params.append(user_id)
        
        if feedback_type:
            query += " AND feedback_type = ?"
            params.append(feedback_type)
        
        query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(query, params)
            
            results = []
            for row in cursor.fetchall():
                feedback_dict = dict(row)
                # 解析JSON字段
                if feedback_dict['tags']:
                    feedback_dict['tags'] = json.loads(feedback_dict['tags'])
                if feedback_dict['metadata']:
                    feedback_dict['metadata'] = json.loads(feedback_dict['metadata'])
                results.append(feedback_dict)
            
            return results
    
    def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """获取用户偏好"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM user_preferences WHERE user_id = ?
                ORDER BY updated_at DESC
            """, (user_id,))
            
            preferences = {}
            for row in cursor.fetchall():
                pref_type = row['preference_type']
                pref_key = row['preference_key']
                pref_value = json.loads(row['preference_value'])
                
                if pref_type not in preferences:
                    preferences[pref_type] = {}
                
                preferences[pref_type][pref_key] = {
                    'value': pref_value,
                    'confidence_score': row['confidence_score'],
                    'source': row['source'],
                    'updated_at': row['updated_at']
                }
            
            return preferences
    
    def get_feedback_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取反馈统计信息"""
        start_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # 基础统计
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total_feedback,
                    AVG(rating) as avg_rating,
                    COUNT(DISTINCT user_id) as unique_users
                FROM user_feedback 
                WHERE created_at >= ?
            """, (start_date,))
            basic_stats = dict(cursor.fetchone())
            
            # 反馈类型分布
            cursor = conn.execute("""
                SELECT feedback_type, COUNT(*) as count
                FROM user_feedback 
                WHERE created_at >= ?
                GROUP BY feedback_type
            """, (start_date,))
            feedback_types = {row['feedback_type']: row['count'] for row in cursor.fetchall()}
            
            # 满意度统计
            cursor = conn.execute("""
                SELECT 
                    AVG(overall_satisfaction) as avg_satisfaction,
                    AVG(likelihood_to_recommend) as avg_nps,
                    COUNT(*) as survey_count
                FROM satisfaction_surveys 
                WHERE created_at >= ?
            """, (start_date,))
            satisfaction_stats = dict(cursor.fetchone())
            
            return {
                'period_days': days,
                'basic_stats': basic_stats,
                'feedback_types': feedback_types,
                'satisfaction_stats': satisfaction_stats,
                'generated_at': datetime.now().isoformat()
            }
    
    def get_top_issues(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取热门问题"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT 
                    feedback_type,
                    content,
                    COUNT(*) as frequency,
                    AVG(rating) as avg_rating,
                    MAX(created_at) as latest_report
                FROM user_feedback 
                WHERE status = 'active' AND rating IS NOT NULL
                GROUP BY feedback_type, content
                HAVING COUNT(*) > 1
                ORDER BY frequency DESC, avg_rating ASC
                LIMIT ?
            """, (limit,))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def calculate_nps_score(self, days: int = 30) -> Dict[str, Any]:
        """计算NPS评分"""
        start_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT likelihood_to_recommend
                FROM satisfaction_surveys 
                WHERE created_at >= ?
            """, (start_date,))
            
            scores = [row[0] for row in cursor.fetchall()]
            
            if not scores:
                return {'nps_score': 0, 'total_responses': 0}
            
            promoters = len([s for s in scores if s >= 9])
            detractors = len([s for s in scores if s <= 6])
            total = len(scores)
            
            nps_score = ((promoters - detractors) / total) * 100 if total > 0 else 0
            
            return {
                'nps_score': round(nps_score, 2),
                'promoters': promoters,
                'passives': total - promoters - detractors,
                'detractors': detractors,
                'total_responses': total
            }

# 全局反馈数据管理器实例
feedback_data_manager = FeedbackDataManager()
