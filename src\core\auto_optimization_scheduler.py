"""
自动优化调度器
定期触发自动优化，实现持续的系统改进
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

try:
    from .auto_optimization_engine import auto_optimization_engine
    from ..config.feature_config import feature_config_manager
except ImportError:
    from src.core.auto_optimization_engine import auto_optimization_engine
    from src.config.feature_config import feature_config_manager

# 配置日志
logger = logging.getLogger(__name__)

class AutoOptimizationScheduler:
    """自动优化调度器"""
    
    def __init__(self, optimization_interval: int = 3600):  # 默认每小时检查一次
        self.optimization_interval = optimization_interval  # 秒
        self.engine = auto_optimization_engine
        self.config_manager = feature_config_manager
        
        self.is_running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.last_optimization_time: Optional[datetime] = None
        self.optimization_count = 0
        
        logger.info(f"自动优化调度器初始化完成，检查间隔: {optimization_interval}秒")
    
    def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已经在运行中")
            return
        
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("自动优化调度器已启动")
    
    def stop(self):
        """停止调度器"""
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        logger.info("自动优化调度器已停止")
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        logger.info("调度器主循环开始")
        
        while self.is_running:
            try:
                # 检查是否启用了自动优化
                if self._is_auto_optimization_enabled():
                    # 检查是否到了优化时间
                    if self._should_run_optimization():
                        logger.info("触发自动优化周期")
                        
                        # 在新的事件循环中运行异步优化
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        
                        try:
                            result = loop.run_until_complete(self.engine.run_optimization_cycle())
                            self._handle_optimization_result(result)
                        finally:
                            loop.close()
                
                # 等待下次检查
                time.sleep(60)  # 每分钟检查一次是否需要优化
                
            except Exception as e:
                logger.error(f"调度器运行异常: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续
    
    def _is_auto_optimization_enabled(self) -> bool:
        """检查是否启用了自动优化"""
        try:
            # 检查配置管理器中的自动优化开关
            configs = self.config_manager.get_all_configs()
            auto_opt_config = configs.get("auto_optimization_enabled")
            
            if auto_opt_config:
                return self.config_manager.is_feature_enabled("auto_optimization_enabled")
            
            # 默认启用自动优化
            return True
            
        except Exception as e:
            logger.error(f"检查自动优化开关失败: {e}")
            return True  # 默认启用
    
    def _should_run_optimization(self) -> bool:
        """检查是否应该运行优化"""
        if not self.last_optimization_time:
            return True  # 第一次运行
        
        time_since_last = datetime.now() - self.last_optimization_time
        return time_since_last.total_seconds() >= self.optimization_interval
    
    def _handle_optimization_result(self, result: Dict[str, Any]):
        """处理优化结果"""
        try:
            self.last_optimization_time = datetime.now()
            self.optimization_count += 1
            
            status = result.get("status", "unknown")
            
            if status == "completed":
                successful_actions = result.get("successful_actions", 0)
                total_actions = result.get("total_actions", 0)
                
                logger.info(f"自动优化完成: {successful_actions}/{total_actions} 个行动成功执行")
                
                # 记录优化历史
                self._record_optimization_history(result)
                
            elif status == "no_actions":
                logger.info("自动优化检查完成，无需执行优化行动")
                
            else:
                logger.warning(f"自动优化异常: {result.get('message', '未知错误')}")
                
        except Exception as e:
            logger.error(f"处理优化结果失败: {e}")
    
    def _record_optimization_history(self, result: Dict[str, Any]):
        """记录优化历史"""
        try:
            # 这里可以将优化历史记录到数据库或文件
            # 简化版本只记录到日志
            actions_executed = result.get("actions_executed", [])
            
            for action in actions_executed:
                logger.info(f"优化行动: {action.get('description')} - {'成功' if action.get('success') else '失败'}")
                
        except Exception as e:
            logger.error(f"记录优化历史失败: {e}")
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            "is_running": self.is_running,
            "optimization_interval": self.optimization_interval,
            "last_optimization_time": self.last_optimization_time.isoformat() if self.last_optimization_time else None,
            "optimization_count": self.optimization_count,
            "next_optimization_time": (
                self.last_optimization_time + timedelta(seconds=self.optimization_interval)
            ).isoformat() if self.last_optimization_time else "立即",
            "auto_optimization_enabled": self._is_auto_optimization_enabled()
        }
    
    async def trigger_immediate_optimization_async(self) -> Dict[str, Any]:
        """立即触发优化（异步版本）"""
        try:
            logger.info("手动触发立即优化")

            result = await self.engine.run_optimization_cycle()
            self._handle_optimization_result(result)

            return {
                "status": "success",
                "message": "立即优化已触发",
                "result": result
            }

        except Exception as e:
            logger.error(f"立即优化失败: {e}")
            return {
                "status": "error",
                "message": f"立即优化失败: {str(e)}"
            }

    def trigger_immediate_optimization(self) -> Dict[str, Any]:
        """立即触发优化（同步版本，用于线程调用）"""
        try:
            logger.info("手动触发立即优化（同步版本）")

            # 在新线程中运行异步优化
            import concurrent.futures

            def run_async_optimization():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    return loop.run_until_complete(self.engine.run_optimization_cycle())
                finally:
                    loop.close()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_async_optimization)
                result = future.result(timeout=30)  # 30秒超时

            self._handle_optimization_result(result)

            return {
                "status": "success",
                "message": "立即优化已触发",
                "result": result
            }

        except Exception as e:
            logger.error(f"立即优化失败: {e}")
            return {
                "status": "error",
                "message": f"立即优化失败: {str(e)}"
            }
    
    def update_optimization_interval(self, new_interval: int):
        """更新优化间隔"""
        if new_interval < 300:  # 最小5分钟
            raise ValueError("优化间隔不能少于5分钟")
        
        old_interval = self.optimization_interval
        self.optimization_interval = new_interval
        
        logger.info(f"优化间隔已更新: {old_interval}秒 -> {new_interval}秒")

# 全局调度器实例
auto_optimization_scheduler = AutoOptimizationScheduler()

# 自动启动调度器
def start_auto_optimization():
    """启动自动优化"""
    try:
        auto_optimization_scheduler.start()
        logger.info("自动优化调度器已启动")
    except Exception as e:
        logger.error(f"启动自动优化调度器失败: {e}")

def stop_auto_optimization():
    """停止自动优化"""
    try:
        auto_optimization_scheduler.stop()
        logger.info("自动优化调度器已停止")
    except Exception as e:
        logger.error(f"停止自动优化调度器失败: {e}")

# 在模块导入时自动启动（可选）
# start_auto_optimization()
