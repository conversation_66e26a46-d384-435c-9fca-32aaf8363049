"""
自动优化迭代引擎
基于用户反馈自动优化系统，实现持续改进和迭代
"""

import logging
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import re

try:
    from ..database.feedback_data_manager import feedback_data_manager
    from ..analytics.feedback_analytics import feedback_analytics, personalization_engine
    from ..config.feature_config import feature_config_manager, FeatureConfig, FeatureStatus, ConfigScope
except ImportError:
    from src.database.feedback_data_manager import feedback_data_manager
    from src.analytics.feedback_analytics import feedback_analytics, personalization_engine
    from src.config.feature_config import feature_config_manager, FeatureConfig, FeatureStatus, ConfigScope

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class OptimizationAction:
    """优化行动"""
    action_id: str
    action_type: str  # feature_toggle, parameter_adjust, ui_optimize, algorithm_improve
    target: str  # 目标组件或功能
    description: str
    priority: int  # 1-5，5为最高优先级
    impact_score: float  # 预期影响评分
    implementation_effort: str  # low, medium, high
    auto_executable: bool  # 是否可以自动执行
    parameters: Dict[str, Any]
    created_at: datetime
    status: str = "pending"  # pending, executing, completed, failed

@dataclass
class OptimizationResult:
    """优化结果"""
    action_id: str
    execution_time: datetime
    success: bool
    impact_metrics: Dict[str, float]
    user_feedback_change: Dict[str, float]
    notes: str

class AutoOptimizationEngine:
    """自动优化迭代引擎"""
    
    def __init__(self):
        self.feedback_manager = feedback_data_manager
        self.analytics = feedback_analytics
        self.personalization = personalization_engine
        self.config_manager = feature_config_manager
        
        # 优化规则库
        self.optimization_rules = self._load_optimization_rules()
        
        # 执行历史
        self.execution_history: List[OptimizationResult] = []
        
        logger.info("自动优化迭代引擎初始化完成")
    
    def _load_optimization_rules(self) -> Dict[str, Any]:
        """加载优化规则库"""
        return {
            # 预测准确率优化规则
            "prediction_accuracy": {
                "keywords": ["准确率", "预测", "精度", "命中率"],
                "actions": [
                    {
                        "type": "algorithm_improve",
                        "target": "prediction_models",
                        "description": "优化预测算法参数",
                        "auto_executable": True,
                        "parameters": {"learning_rate_adjustment": 0.1, "feature_importance_threshold": 0.05}
                    },
                    {
                        "type": "feature_toggle",
                        "target": "advanced_analytics",
                        "description": "启用高级分析功能",
                        "auto_executable": True,
                        "parameters": {"feature_name": "advanced_analytics", "enabled": True}
                    }
                ]
            },
            
            # 界面优化规则
            "ui_complexity": {
                "keywords": ["界面", "操作", "复杂", "简化", "易用"],
                "actions": [
                    {
                        "type": "ui_optimize",
                        "target": "user_interface",
                        "description": "简化用户界面",
                        "auto_executable": True,
                        "parameters": {"simplify_navigation": True, "reduce_steps": True}
                    },
                    {
                        "type": "feature_toggle",
                        "target": "guided_tour",
                        "description": "启用操作引导功能",
                        "auto_executable": True,
                        "parameters": {"feature_name": "guided_tour", "enabled": True}
                    }
                ]
            },
            
            # 功能完整性优化规则
            "feature_completeness": {
                "keywords": ["功能", "增加", "缺少", "需要", "希望"],
                "actions": [
                    {
                        "type": "feature_toggle",
                        "target": "new_features",
                        "description": "启用新功能模块",
                        "auto_executable": True,
                        "parameters": {"feature_name": "enhanced_features", "enabled": True}
                    }
                ]
            },
            
            # 自动优化规则
            "auto_optimization": {
                "keywords": ["自动", "优化", "迭代", "改进"],
                "actions": [
                    {
                        "type": "feature_toggle",
                        "target": "auto_optimization",
                        "description": "启用自动优化系统",
                        "auto_executable": True,
                        "parameters": {"feature_name": "auto_optimization_enabled", "enabled": True}
                    },
                    {
                        "type": "parameter_adjust",
                        "target": "optimization_frequency",
                        "description": "调整优化频率",
                        "auto_executable": True,
                        "parameters": {"optimization_interval": 3600}  # 每小时检查一次
                    }
                ]
            }
        }
    
    async def analyze_feedback_and_optimize(self) -> List[OptimizationAction]:
        """分析反馈并生成优化行动"""
        try:
            # 获取最近的反馈数据
            recent_feedback = self.feedback_manager.get_user_feedback(limit=50)
            
            if not recent_feedback:
                logger.info("没有新的反馈数据，跳过优化分析")
                return []
            
            # 分析反馈内容
            optimization_actions = []
            
            # 1. 关键词分析
            keyword_analysis = self._analyze_feedback_keywords(recent_feedback)
            
            # 2. 满意度分析
            satisfaction_analysis = self._analyze_satisfaction_trends(recent_feedback)
            
            # 3. 生成优化行动
            for category, score in keyword_analysis.items():
                if score > 0.3:  # 阈值：30%的反馈提到相关关键词
                    actions = self._generate_optimization_actions(category, score)
                    optimization_actions.extend(actions)
            
            # 4. 基于满意度生成行动
            if satisfaction_analysis["avg_rating"] < 4.0:
                urgent_actions = self._generate_urgent_optimization_actions(satisfaction_analysis)
                optimization_actions.extend(urgent_actions)
            
            # 5. 优先级排序
            optimization_actions.sort(key=lambda x: x.priority, reverse=True)
            
            logger.info(f"生成了 {len(optimization_actions)} 个优化行动")
            return optimization_actions
            
        except Exception as e:
            logger.error(f"分析反馈并生成优化行动失败: {e}")
            return []
    
    def _analyze_feedback_keywords(self, feedback_list: List[Dict]) -> Dict[str, float]:
        """分析反馈关键词"""
        keyword_scores = {}
        total_feedback = len(feedback_list)
        
        for category, rules in self.optimization_rules.items():
            keywords = rules["keywords"]
            matching_count = 0
            
            for feedback in feedback_list:
                content = feedback.get("content", "").lower()
                if any(keyword in content for keyword in keywords):
                    matching_count += 1
            
            keyword_scores[category] = matching_count / total_feedback if total_feedback > 0 else 0
        
        return keyword_scores
    
    def _analyze_satisfaction_trends(self, feedback_list: List[Dict]) -> Dict[str, float]:
        """分析满意度趋势"""
        ratings = [f["rating"] for f in feedback_list if f.get("rating")]
        
        if not ratings:
            return {"avg_rating": 0, "trend": "stable"}
        
        avg_rating = sum(ratings) / len(ratings)
        
        # 简单趋势分析（最近一半 vs 前一半）
        mid_point = len(ratings) // 2
        if mid_point > 0:
            recent_avg = sum(ratings[:mid_point]) / mid_point
            older_avg = sum(ratings[mid_point:]) / (len(ratings) - mid_point)
            trend = "improving" if recent_avg > older_avg else "declining" if recent_avg < older_avg else "stable"
        else:
            trend = "stable"
        
        return {
            "avg_rating": avg_rating,
            "trend": trend,
            "total_ratings": len(ratings)
        }
    
    def _generate_optimization_actions(self, category: str, score: float) -> List[OptimizationAction]:
        """生成优化行动"""
        actions = []
        
        if category not in self.optimization_rules:
            return actions
        
        rule_actions = self.optimization_rules[category]["actions"]
        
        for action_config in rule_actions:
            action = OptimizationAction(
                action_id=f"{category}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(actions)}",
                action_type=action_config["type"],
                target=action_config["target"],
                description=action_config["description"],
                priority=min(5, int(score * 5) + 1),  # 基于关键词匹配度计算优先级
                impact_score=score,
                implementation_effort=action_config.get("implementation_effort", "medium"),
                auto_executable=action_config["auto_executable"],
                parameters=action_config["parameters"],
                created_at=datetime.now()
            )
            actions.append(action)
        
        return actions
    
    def _generate_urgent_optimization_actions(self, satisfaction_analysis: Dict) -> List[OptimizationAction]:
        """生成紧急优化行动"""
        actions = []
        
        if satisfaction_analysis["avg_rating"] < 3.0:
            # 满意度很低，生成紧急行动
            urgent_action = OptimizationAction(
                action_id=f"urgent_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                action_type="feature_toggle",
                target="emergency_improvements",
                description="启用紧急改进模式",
                priority=5,
                impact_score=1.0,
                implementation_effort="low",
                auto_executable=True,
                parameters={"feature_name": "emergency_mode", "enabled": True},
                created_at=datetime.now()
            )
            actions.append(urgent_action)
        
        return actions
    
    async def execute_optimization_actions(self, actions: List[OptimizationAction]) -> List[OptimizationResult]:
        """执行优化行动"""
        results = []
        
        for action in actions:
            if not action.auto_executable:
                logger.info(f"跳过非自动执行的行动: {action.description}")
                continue
            
            try:
                logger.info(f"执行优化行动: {action.description}")
                result = await self._execute_single_action(action)
                results.append(result)
                
                # 记录执行历史
                self.execution_history.append(result)
                
            except Exception as e:
                logger.error(f"执行优化行动失败 {action.action_id}: {e}")
                
                error_result = OptimizationResult(
                    action_id=action.action_id,
                    execution_time=datetime.now(),
                    success=False,
                    impact_metrics={},
                    user_feedback_change={},
                    notes=f"执行失败: {str(e)}"
                )
                results.append(error_result)
        
        return results
    
    async def _execute_single_action(self, action: OptimizationAction) -> OptimizationResult:
        """执行单个优化行动"""
        start_time = datetime.now()
        
        try:
            if action.action_type == "feature_toggle":
                success = await self._execute_feature_toggle(action)
            elif action.action_type == "parameter_adjust":
                success = await self._execute_parameter_adjust(action)
            elif action.action_type == "ui_optimize":
                success = await self._execute_ui_optimize(action)
            elif action.action_type == "algorithm_improve":
                success = await self._execute_algorithm_improve(action)
            else:
                success = False
                logger.warning(f"未知的行动类型: {action.action_type}")
            
            # 计算影响指标（简化版本）
            impact_metrics = await self._calculate_impact_metrics(action)
            
            return OptimizationResult(
                action_id=action.action_id,
                execution_time=start_time,
                success=success,
                impact_metrics=impact_metrics,
                user_feedback_change={},  # 需要时间观察
                notes=f"成功执行: {action.description}" if success else "执行失败"
            )
            
        except Exception as e:
            return OptimizationResult(
                action_id=action.action_id,
                execution_time=start_time,
                success=False,
                impact_metrics={},
                user_feedback_change={},
                notes=f"执行异常: {str(e)}"
            )
    
    async def _execute_feature_toggle(self, action: OptimizationAction) -> bool:
        """执行功能开关操作"""
        try:
            params = action.parameters
            feature_name = params.get("feature_name")
            enabled = params.get("enabled", True)
            
            if not feature_name:
                return False
            
            # 创建或更新功能配置
            config = FeatureConfig(
                name=feature_name,
                status=FeatureStatus.ENABLED if enabled else FeatureStatus.DISABLED,
                scope=ConfigScope.GLOBAL,
                description=f"自动优化: {action.description}",
                created_at=datetime.now()
            )
            
            self.config_manager.set_feature_config(
                config,
                changed_by="auto_optimization_engine",
                reason=f"基于用户反馈自动优化: {action.description}"
            )
            
            logger.info(f"功能开关操作成功: {feature_name} = {enabled}")
            return True
            
        except Exception as e:
            logger.error(f"功能开关操作失败: {e}")
            return False
    
    async def _execute_parameter_adjust(self, action: OptimizationAction) -> bool:
        """执行参数调整操作"""
        try:
            # 这里可以调整系统参数
            # 实际实现中需要根据具体的参数类型进行调整
            logger.info(f"参数调整操作: {action.parameters}")
            return True
        except Exception as e:
            logger.error(f"参数调整操作失败: {e}")
            return False
    
    async def _execute_ui_optimize(self, action: OptimizationAction) -> bool:
        """执行UI优化操作"""
        try:
            # 这里可以调整UI配置
            params = action.parameters
            
            if params.get("simplify_navigation"):
                # 启用简化导航功能
                config = FeatureConfig(
                    name="simplified_navigation",
                    status=FeatureStatus.ENABLED,
                    scope=ConfigScope.GLOBAL,
                    description="简化导航界面",
                    value={"simplified": True}
                )
                self.config_manager.set_feature_config(config, changed_by="auto_optimization_engine")
            
            if params.get("reduce_steps"):
                # 启用步骤简化功能
                config = FeatureConfig(
                    name="reduced_steps",
                    status=FeatureStatus.ENABLED,
                    scope=ConfigScope.GLOBAL,
                    description="减少操作步骤",
                    value={"reduced": True}
                )
                self.config_manager.set_feature_config(config, changed_by="auto_optimization_engine")
            
            logger.info(f"UI优化操作成功: {params}")
            return True
            
        except Exception as e:
            logger.error(f"UI优化操作失败: {e}")
            return False
    
    async def _execute_algorithm_improve(self, action: OptimizationAction) -> bool:
        """执行算法改进操作"""
        try:
            # 这里可以调整算法参数
            params = action.parameters
            
            # 创建算法优化配置
            config = FeatureConfig(
                name="algorithm_optimization",
                status=FeatureStatus.ENABLED,
                scope=ConfigScope.GLOBAL,
                description="算法参数优化",
                value=params
            )
            self.config_manager.set_feature_config(config, changed_by="auto_optimization_engine")
            
            logger.info(f"算法改进操作成功: {params}")
            return True
            
        except Exception as e:
            logger.error(f"算法改进操作失败: {e}")
            return False
    
    async def _calculate_impact_metrics(self, action: OptimizationAction) -> Dict[str, float]:
        """计算影响指标"""
        # 简化版本，实际项目中需要更复杂的指标计算
        return {
            "expected_satisfaction_improvement": action.impact_score * 0.1,
            "implementation_success_rate": 0.9,
            "user_adoption_rate": 0.8
        }
    
    async def run_optimization_cycle(self) -> Dict[str, Any]:
        """运行完整的优化周期"""
        try:
            logger.info("开始自动优化周期")
            
            # 1. 分析反馈并生成优化行动
            actions = await self.analyze_feedback_and_optimize()
            
            if not actions:
                return {
                    "status": "no_actions",
                    "message": "没有需要执行的优化行动",
                    "timestamp": datetime.now().isoformat()
                }
            
            # 2. 执行优化行动
            results = await self.execute_optimization_actions(actions)
            
            # 3. 统计结果
            successful_actions = [r for r in results if r.success]
            failed_actions = [r for r in results if not r.success]
            
            summary = {
                "status": "completed",
                "total_actions": len(actions),
                "successful_actions": len(successful_actions),
                "failed_actions": len(failed_actions),
                "success_rate": len(successful_actions) / len(actions) if actions else 0,
                "actions_executed": [
                    {
                        "action_id": action.action_id,
                        "description": action.description,
                        "success": any(r.action_id == action.action_id and r.success for r in results)
                    }
                    for action in actions
                ],
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"优化周期完成: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"优化周期执行失败: {e}")
            return {
                "status": "error",
                "message": f"优化周期执行失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """获取优化状态"""
        return {
            "engine_status": "active",
            "total_executions": len(self.execution_history),
            "recent_executions": [
                {
                    "action_id": result.action_id,
                    "execution_time": result.execution_time.isoformat(),
                    "success": result.success,
                    "notes": result.notes
                }
                for result in self.execution_history[-10:]  # 最近10次执行
            ],
            "optimization_rules_count": len(self.optimization_rules),
            "last_check": datetime.now().isoformat()
        }

# 全局自动优化引擎实例
auto_optimization_engine = AutoOptimizationEngine()
