"""
数据流管理API路由
提供数据流统计、缓存管理、数据同步接口
"""

from fastapi import APIRouter, HTTPException, Query, Body
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel

try:
    from ...core.data_flow_manager import data_flow_manager, DataRequest
except ImportError:
    from src.core.data_flow_manager import data_flow_manager, DataRequest

router = APIRouter(prefix="/api/dataflow", tags=["数据流管理"])

class DataQueryRequest(BaseModel):
    """数据查询请求模型"""
    source: str
    query: str
    params: Optional[Dict[str, Any]] = None
    cache_key: Optional[str] = None
    ttl: Optional[int] = None
    priority: int = 1

@router.get("/stats")
async def get_dataflow_stats():
    """获取数据流统计信息"""
    try:
        stats = data_flow_manager.get_stats()
        
        # 计算缓存命中率
        total_requests = stats.get('total_requests', 0)
        cache_hits = stats.get('cache_hits', 0)
        cache_hit_rate = (cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "status": "success",
            "data": {
                **stats,
                "cache_hit_rate": round(cache_hit_rate, 2),
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据流统计失败: {str(e)}")

@router.post("/query")
async def execute_data_query(request: DataQueryRequest):
    """执行数据查询"""
    try:
        data_request = DataRequest(
            source=request.source,
            query=request.query,
            params=request.params,
            cache_key=request.cache_key,
            ttl=request.ttl,
            priority=request.priority
        )
        
        response = await data_flow_manager.get_data(data_request)
        
        return {
            "status": "success",
            "data": {
                "result": response.data,
                "source": response.source,
                "timestamp": response.timestamp.isoformat(),
                "cache_hit": response.cache_hit,
                "execution_time": response.execution_time
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行数据查询失败: {str(e)}")

@router.post("/cache/clear")
async def clear_cache(pattern: Optional[str] = Body(None, embed=True)):
    """清理缓存"""
    try:
        data_flow_manager.clear_cache(pattern)
        
        return {
            "status": "success",
            "message": f"缓存清理完成" + (f"（模式: {pattern}）" if pattern else "（全部）")
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理缓存失败: {str(e)}")

@router.get("/cache/info")
async def get_cache_info():
    """获取缓存信息"""
    try:
        stats = data_flow_manager.get_stats()
        
        cache_info = {
            "memory_cache_size": stats.get('memory_cache_size', 0),
            "total_requests": stats.get('total_requests', 0),
            "cache_hits": stats.get('cache_hits', 0),
            "db_queries": stats.get('db_queries', 0),
            "cache_hit_rate": 0
        }
        
        if cache_info['total_requests'] > 0:
            cache_info['cache_hit_rate'] = round(
                cache_info['cache_hits'] / cache_info['total_requests'] * 100, 2
            )
        
        return {
            "status": "success",
            "data": cache_info
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存信息失败: {str(e)}")

@router.get("/health")
async def get_dataflow_health():
    """获取数据流健康状态"""
    try:
        stats = data_flow_manager.get_stats()
        
        # 健康状态评估
        health_score = 100
        issues = []
        
        # 检查错误率
        total_requests = stats.get('total_requests', 0)
        errors = stats.get('errors', 0)
        if total_requests > 0:
            error_rate = errors / total_requests * 100
            if error_rate > 5:
                health_score -= 30
                issues.append(f"错误率过高: {error_rate:.1f}%")
            elif error_rate > 1:
                health_score -= 15
                issues.append(f"错误率偏高: {error_rate:.1f}%")
        
        # 检查缓存命中率
        cache_hits = stats.get('cache_hits', 0)
        if total_requests > 0:
            cache_hit_rate = cache_hits / total_requests * 100
            if cache_hit_rate < 30:
                health_score -= 20
                issues.append(f"缓存命中率过低: {cache_hit_rate:.1f}%")
        
        # 检查连接池状态
        connection_pools = stats.get('connection_pools', {})
        for db_path, pool_size in connection_pools.items():
            if pool_size < 3:
                health_score -= 10
                issues.append(f"数据库连接池不足: {db_path} ({pool_size}个连接)")
        
        # 确定健康状态
        if health_score >= 90:
            status = "healthy"
            color = "green"
        elif health_score >= 70:
            status = "warning"
            color = "orange"
        else:
            status = "critical"
            color = "red"
        
        return {
            "status": "success",
            "data": {
                "health_status": status,
                "health_score": max(0, health_score),
                "color": color,
                "issues": issues,
                "metrics": {
                    "total_requests": total_requests,
                    "cache_hit_rate": round(cache_hits / total_requests * 100, 2) if total_requests > 0 else 0,
                    "error_rate": round(errors / total_requests * 100, 2) if total_requests > 0 else 0,
                    "memory_cache_size": stats.get('memory_cache_size', 0)
                },
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据流健康状态失败: {str(e)}")

@router.get("/connections")
async def get_connection_status():
    """获取数据库连接状态"""
    try:
        stats = data_flow_manager.get_stats()
        connection_pools = stats.get('connection_pools', {})
        
        connection_info = []
        for db_path, pool_size in connection_pools.items():
            db_name = db_path.split('/')[-1] if '/' in db_path else db_path.split('\\')[-1]
            
            connection_info.append({
                "database": db_name,
                "path": db_path,
                "pool_size": pool_size,
                "status": "healthy" if pool_size >= 3 else "warning" if pool_size >= 1 else "critical"
            })
        
        return {
            "status": "success",
            "data": {
                "connections": connection_info,
                "total_databases": len(connection_info)
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取连接状态失败: {str(e)}")

@router.get("/performance")
async def get_dataflow_performance():
    """获取数据流性能指标"""
    try:
        stats = data_flow_manager.get_stats()
        
        # 计算性能指标
        total_requests = stats.get('total_requests', 0)
        cache_hits = stats.get('cache_hits', 0)
        db_queries = stats.get('db_queries', 0)
        errors = stats.get('errors', 0)
        
        performance_metrics = {
            "total_requests": total_requests,
            "cache_hits": cache_hits,
            "db_queries": db_queries,
            "errors": errors,
            "cache_hit_rate": round(cache_hits / total_requests * 100, 2) if total_requests > 0 else 0,
            "error_rate": round(errors / total_requests * 100, 2) if total_requests > 0 else 0,
            "db_query_rate": round(db_queries / total_requests * 100, 2) if total_requests > 0 else 0
        }
        
        # 性能评级
        cache_hit_rate = performance_metrics["cache_hit_rate"]
        error_rate = performance_metrics["error_rate"]
        
        if cache_hit_rate >= 80 and error_rate <= 1:
            performance_grade = "A"
            performance_color = "green"
        elif cache_hit_rate >= 60 and error_rate <= 3:
            performance_grade = "B"
            performance_color = "blue"
        elif cache_hit_rate >= 40 and error_rate <= 5:
            performance_grade = "C"
            performance_color = "orange"
        else:
            performance_grade = "D"
            performance_color = "red"
        
        return {
            "status": "success",
            "data": {
                "metrics": performance_metrics,
                "performance_grade": performance_grade,
                "performance_color": performance_color,
                "recommendations": _get_performance_recommendations(performance_metrics)
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}")

def _get_performance_recommendations(metrics: Dict[str, Any]) -> List[str]:
    """获取性能优化建议"""
    recommendations = []
    
    cache_hit_rate = metrics.get("cache_hit_rate", 0)
    error_rate = metrics.get("error_rate", 0)
    
    if cache_hit_rate < 50:
        recommendations.append("缓存命中率较低，建议增加缓存TTL或优化缓存策略")
    
    if error_rate > 3:
        recommendations.append("错误率偏高，建议检查数据库连接和查询语句")
    
    if metrics.get("db_queries", 0) > metrics.get("cache_hits", 0):
        recommendations.append("数据库查询频繁，建议优化缓存配置")
    
    if not recommendations:
        recommendations.append("系统运行良好，继续保持当前配置")
    
    return recommendations

@router.post("/test-query")
async def test_data_query():
    """测试数据查询功能"""
    try:
        # 执行一个简单的测试查询
        test_request = DataRequest(
            source="business",
            query="SELECT COUNT(*) as count FROM lottery_data LIMIT 1",
            params={},
            cache_key="test_query",
            ttl=60
        )
        
        response = await data_flow_manager.get_data(test_request)
        
        return {
            "status": "success",
            "message": "数据查询测试成功",
            "data": {
                "test_result": response.data,
                "cache_hit": response.cache_hit,
                "execution_time": response.execution_time,
                "timestamp": response.timestamp.isoformat()
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"数据查询测试失败: {str(e)}",
            "data": None
        }

@router.get("/dashboard")
async def get_dataflow_dashboard():
    """获取数据流管理仪表板"""
    try:
        stats = data_flow_manager.get_stats()
        
        # 基础统计
        total_requests = stats.get('total_requests', 0)
        cache_hits = stats.get('cache_hits', 0)
        db_queries = stats.get('db_queries', 0)
        errors = stats.get('errors', 0)
        
        # 计算比率
        cache_hit_rate = round(cache_hits / total_requests * 100, 2) if total_requests > 0 else 0
        error_rate = round(errors / total_requests * 100, 2) if total_requests > 0 else 0
        
        # 连接池状态
        connection_pools = stats.get('connection_pools', {})
        total_connections = sum(connection_pools.values())
        
        # 订阅者统计
        subscribers = stats.get('subscribers', {})
        total_subscribers = sum(subscribers.values())
        
        dashboard_data = {
            "overview": {
                "total_requests": total_requests,
                "cache_hit_rate": cache_hit_rate,
                "error_rate": error_rate,
                "total_connections": total_connections,
                "total_subscribers": total_subscribers
            },
            "performance": {
                "cache_hits": cache_hits,
                "db_queries": db_queries,
                "errors": errors,
                "memory_cache_size": stats.get('memory_cache_size', 0)
            },
            "connections": [
                {
                    "database": db_path.split('/')[-1] if '/' in db_path else db_path.split('\\')[-1],
                    "pool_size": pool_size
                }
                for db_path, pool_size in connection_pools.items()
            ],
            "health_status": "healthy" if error_rate < 1 and cache_hit_rate > 50 else "warning" if error_rate < 5 else "critical"
        }
        
        return {
            "status": "success",
            "data": dashboard_data,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据流仪表板失败: {str(e)}")
