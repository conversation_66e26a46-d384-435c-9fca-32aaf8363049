"""
统一数据流管理器
实现模块间数据共享机制，添加数据缓存和同步策略
"""

import asyncio
import logging
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from pathlib import Path
import json
import hashlib
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DataFlowConfig:
    """数据流配置"""
    # 缓存配置
    memory_cache_size: int = 1000
    memory_cache_ttl: int = 300  # 5分钟
    db_cache_enabled: bool = True
    db_cache_ttl: int = 3600  # 1小时
    
    # 数据库配置
    primary_db_path: str = "data/lottery.db"
    business_db_path: str = "data/fucai3d.db"
    cache_db_path: str = "data/cache.db"
    
    # 同步配置
    sync_interval: int = 60  # 1分钟
    batch_size: int = 100
    max_workers: int = 4
    
    # 性能配置
    connection_pool_size: int = 10
    query_timeout: int = 30

@dataclass
class DataRequest:
    """数据请求"""
    source: str  # 数据源标识
    query: str   # 查询语句或标识
    params: Dict[str, Any] = None
    cache_key: str = None
    ttl: int = None
    priority: int = 1  # 优先级 1-5

@dataclass
class DataResponse:
    """数据响应"""
    data: Any
    source: str
    timestamp: datetime
    cache_hit: bool = False
    execution_time: float = 0.0

class DataFlowManager:
    """统一数据流管理器"""
    
    def __init__(self, config: DataFlowConfig = None):
        self.config = config or DataFlowConfig()
        
        # 内存缓存
        self._memory_cache: Dict[str, Dict] = {}
        self._cache_access_times: Dict[str, datetime] = {}
        self._cache_lock = threading.RLock()
        
        # 数据库连接池
        self._connection_pools: Dict[str, List] = defaultdict(list)
        self._pool_locks: Dict[str, threading.Lock] = defaultdict(threading.Lock)
        
        # 数据流统计
        self._stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'db_queries': 0,
            'sync_operations': 0,
            'errors': 0
        }
        
        # 数据订阅者
        self._subscribers: Dict[str, List[Callable]] = defaultdict(list)
        
        # 同步队列
        self._sync_queue: deque = deque()
        self._sync_lock = threading.Lock()
        
        # 线程池
        self._executor = ThreadPoolExecutor(max_workers=self.config.max_workers)
        
        # 初始化
        self._init_databases()
        self._start_background_tasks()
        
        logger.info("数据流管理器初始化完成")
    
    def _init_databases(self):
        """初始化数据库连接"""
        databases = [
            self.config.primary_db_path,
            self.config.business_db_path,
            self.config.cache_db_path
        ]
        
        for db_path in databases:
            Path(db_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 创建连接池
            for _ in range(self.config.connection_pool_size):
                try:
                    conn = sqlite3.connect(
                        db_path,
                        timeout=self.config.query_timeout,
                        check_same_thread=False
                    )
                    conn.row_factory = sqlite3.Row
                    self._connection_pools[db_path].append(conn)
                except Exception as e:
                    logger.error(f"创建数据库连接失败 {db_path}: {e}")
        
        # 初始化缓存表
        self._init_cache_table()
    
    def _init_cache_table(self):
        """初始化缓存表"""
        with self._get_connection(self.config.cache_db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS data_cache (
                    cache_key TEXT PRIMARY KEY,
                    data TEXT NOT NULL,
                    source TEXT NOT NULL,
                    created_at DATETIME NOT NULL,
                    expires_at DATETIME NOT NULL,
                    access_count INTEGER DEFAULT 0,
                    last_access DATETIME
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_cache_expires 
                ON data_cache(expires_at)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_cache_source 
                ON data_cache(source)
            """)
    
    @contextmanager
    def _get_connection(self, db_path: str):
        """获取数据库连接"""
        with self._pool_locks[db_path]:
            if self._connection_pools[db_path]:
                conn = self._connection_pools[db_path].pop()
            else:
                conn = sqlite3.connect(
                    db_path,
                    timeout=self.config.query_timeout,
                    check_same_thread=False
                )
                conn.row_factory = sqlite3.Row
        
        try:
            yield conn
        finally:
            with self._pool_locks[db_path]:
                self._connection_pools[db_path].append(conn)
    
    def _generate_cache_key(self, request: DataRequest) -> str:
        """生成缓存键"""
        if request.cache_key:
            return request.cache_key
        
        # 基于请求内容生成哈希
        content = f"{request.source}:{request.query}:{json.dumps(request.params, sort_keys=True)}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_from_memory_cache(self, cache_key: str) -> Optional[DataResponse]:
        """从内存缓存获取数据"""
        with self._cache_lock:
            if cache_key in self._memory_cache:
                cache_data = self._memory_cache[cache_key]
                
                # 检查是否过期
                if datetime.now() < cache_data['expires_at']:
                    # 更新访问时间
                    self._cache_access_times[cache_key] = datetime.now()
                    
                    return DataResponse(
                        data=cache_data['data'],
                        source=cache_data['source'],
                        timestamp=cache_data['timestamp'],
                        cache_hit=True
                    )
                else:
                    # 过期，删除
                    del self._memory_cache[cache_key]
                    if cache_key in self._cache_access_times:
                        del self._cache_access_times[cache_key]
        
        return None
    
    def _save_to_memory_cache(self, cache_key: str, response: DataResponse, ttl: int):
        """保存到内存缓存"""
        with self._cache_lock:
            # 检查缓存大小，如果超限则清理最久未访问的
            if len(self._memory_cache) >= self.config.memory_cache_size:
                self._cleanup_memory_cache()
            
            expires_at = datetime.now() + timedelta(seconds=ttl)
            
            self._memory_cache[cache_key] = {
                'data': response.data,
                'source': response.source,
                'timestamp': response.timestamp,
                'expires_at': expires_at
            }
            
            self._cache_access_times[cache_key] = datetime.now()
    
    def _cleanup_memory_cache(self):
        """清理内存缓存"""
        # 删除过期的缓存
        now = datetime.now()
        expired_keys = []
        
        for key, cache_data in self._memory_cache.items():
            if now >= cache_data['expires_at']:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._memory_cache[key]
            if key in self._cache_access_times:
                del self._cache_access_times[key]
        
        # 如果还是超限，删除最久未访问的
        if len(self._memory_cache) >= self.config.memory_cache_size:
            # 按访问时间排序，删除最旧的
            sorted_keys = sorted(
                self._cache_access_times.keys(),
                key=lambda k: self._cache_access_times[k]
            )
            
            keys_to_remove = sorted_keys[:len(sorted_keys) // 4]  # 删除25%
            for key in keys_to_remove:
                if key in self._memory_cache:
                    del self._memory_cache[key]
                if key in self._cache_access_times:
                    del self._cache_access_times[key]
    
    def _get_from_db_cache(self, cache_key: str) -> Optional[Any]:
        """从数据库缓存获取数据"""
        if not self.config.db_cache_enabled:
            return None
        
        try:
            with self._get_connection(self.config.cache_db_path) as conn:
                cursor = conn.execute("""
                    SELECT data, source, created_at 
                    FROM data_cache 
                    WHERE cache_key = ? AND expires_at > ?
                """, (cache_key, datetime.now()))
                
                row = cursor.fetchone()
                if row:
                    # 更新访问统计
                    conn.execute("""
                        UPDATE data_cache 
                        SET access_count = access_count + 1, last_access = ?
                        WHERE cache_key = ?
                    """, (datetime.now(), cache_key))
                    
                    return json.loads(row['data'])
        
        except Exception as e:
            logger.error(f"数据库缓存读取失败: {e}")
        
        return None
    
    def _save_to_db_cache(self, cache_key: str, response: DataResponse, ttl: int):
        """保存到数据库缓存"""
        if not self.config.db_cache_enabled:
            return
        
        try:
            expires_at = datetime.now() + timedelta(seconds=ttl)
            
            with self._get_connection(self.config.cache_db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO data_cache 
                    (cache_key, data, source, created_at, expires_at, access_count, last_access)
                    VALUES (?, ?, ?, ?, ?, 1, ?)
                """, (
                    cache_key,
                    json.dumps(response.data),
                    response.source,
                    response.timestamp,
                    expires_at,
                    datetime.now()
                ))
        
        except Exception as e:
            logger.error(f"数据库缓存保存失败: {e}")
    
    async def get_data(self, request: DataRequest) -> DataResponse:
        """获取数据（主要接口）"""
        start_time = time.time()
        self._stats['total_requests'] += 1
        
        # 生成缓存键
        cache_key = self._generate_cache_key(request)
        
        # 1. 尝试从内存缓存获取
        cached_response = self._get_from_memory_cache(cache_key)
        if cached_response:
            self._stats['cache_hits'] += 1
            cached_response.execution_time = time.time() - start_time
            return cached_response
        
        # 2. 尝试从数据库缓存获取
        cached_data = self._get_from_db_cache(cache_key)
        if cached_data:
            response = DataResponse(
                data=cached_data,
                source=request.source,
                timestamp=datetime.now(),
                cache_hit=True,
                execution_time=time.time() - start_time
            )
            
            # 保存到内存缓存
            ttl = request.ttl or self.config.memory_cache_ttl
            self._save_to_memory_cache(cache_key, response, ttl)
            
            self._stats['cache_hits'] += 1
            return response
        
        # 3. 从数据源获取
        try:
            data = await self._fetch_from_source(request)
            response = DataResponse(
                data=data,
                source=request.source,
                timestamp=datetime.now(),
                cache_hit=False,
                execution_time=time.time() - start_time
            )
            
            # 保存到缓存
            memory_ttl = request.ttl or self.config.memory_cache_ttl
            db_ttl = request.ttl or self.config.db_cache_ttl
            
            self._save_to_memory_cache(cache_key, response, memory_ttl)
            self._save_to_db_cache(cache_key, response, db_ttl)
            
            self._stats['db_queries'] += 1
            
            # 通知订阅者
            await self._notify_subscribers(request.source, data)
            
            return response
            
        except Exception as e:
            self._stats['errors'] += 1
            logger.error(f"数据获取失败: {e}")
            raise
    
    async def _fetch_from_source(self, request: DataRequest) -> Any:
        """从数据源获取数据"""
        # 根据数据源类型选择数据库
        if request.source.startswith('lottery'):
            db_path = self.config.primary_db_path
        elif request.source.startswith('business'):
            db_path = self.config.business_db_path
        else:
            db_path = self.config.business_db_path  # 默认
        
        # 在线程池中执行数据库查询
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor,
            self._execute_query,
            db_path,
            request.query,
            request.params or {}
        )
    
    def _execute_query(self, db_path: str, query: str, params: Dict[str, Any]) -> Any:
        """执行数据库查询"""
        with self._get_connection(db_path) as conn:
            cursor = conn.execute(query, params)
            
            if query.strip().upper().startswith('SELECT'):
                # 查询操作
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
            else:
                # 修改操作
                conn.commit()
                return cursor.rowcount
    
    def subscribe(self, source: str, callback: Callable[[Any], None]):
        """订阅数据更新"""
        self._subscribers[source].append(callback)
    
    def unsubscribe(self, source: str, callback: Callable[[Any], None]):
        """取消订阅"""
        if callback in self._subscribers[source]:
            self._subscribers[source].remove(callback)
    
    async def _notify_subscribers(self, source: str, data: Any):
        """通知订阅者"""
        for callback in self._subscribers[source]:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception as e:
                logger.error(f"通知订阅者失败: {e}")
    
    def _start_background_tasks(self):
        """启动后台任务"""
        # 启动缓存清理任务
        threading.Thread(
            target=self._cache_cleanup_worker,
            daemon=True
        ).start()
        
        # 启动同步任务
        threading.Thread(
            target=self._sync_worker,
            daemon=True
        ).start()
        
        logger.info("后台任务启动完成")
    
    def _cache_cleanup_worker(self):
        """缓存清理工作线程"""
        while True:
            try:
                time.sleep(300)  # 5分钟清理一次
                
                # 清理内存缓存
                with self._cache_lock:
                    self._cleanup_memory_cache()
                
                # 清理数据库缓存
                if self.config.db_cache_enabled:
                    with self._get_connection(self.config.cache_db_path) as conn:
                        conn.execute("DELETE FROM data_cache WHERE expires_at < ?", (datetime.now(),))
                        conn.commit()
                
                logger.debug("缓存清理完成")
                
            except Exception as e:
                logger.error(f"缓存清理失败: {e}")
    
    def _sync_worker(self):
        """数据同步工作线程"""
        while True:
            try:
                time.sleep(self.config.sync_interval)
                
                # 处理同步队列
                with self._sync_lock:
                    if self._sync_queue:
                        sync_tasks = list(self._sync_queue)
                        self._sync_queue.clear()
                        
                        for task in sync_tasks:
                            self._process_sync_task(task)
                            self._stats['sync_operations'] += 1
                
            except Exception as e:
                logger.error(f"数据同步失败: {e}")
    
    def _process_sync_task(self, task: Dict[str, Any]):
        """处理同步任务"""
        # 这里可以实现具体的同步逻辑
        # 例如：主从数据库同步、缓存预热等
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self._stats,
            'memory_cache_size': len(self._memory_cache),
            'connection_pools': {
                db: len(pool) for db, pool in self._connection_pools.items()
            },
            'subscribers': {
                source: len(callbacks) for source, callbacks in self._subscribers.items()
            }
        }
    
    def clear_cache(self, pattern: str = None):
        """清理缓存"""
        with self._cache_lock:
            if pattern:
                # 清理匹配模式的缓存
                keys_to_remove = [
                    key for key in self._memory_cache.keys()
                    if pattern in key
                ]
                for key in keys_to_remove:
                    del self._memory_cache[key]
                    if key in self._cache_access_times:
                        del self._cache_access_times[key]
            else:
                # 清理所有缓存
                self._memory_cache.clear()
                self._cache_access_times.clear()
        
        # 清理数据库缓存
        if self.config.db_cache_enabled:
            with self._get_connection(self.config.cache_db_path) as conn:
                if pattern:
                    conn.execute("DELETE FROM data_cache WHERE cache_key LIKE ?", (f"%{pattern}%",))
                else:
                    conn.execute("DELETE FROM data_cache")
                conn.commit()

# 全局数据流管理器实例
data_flow_manager = DataFlowManager()
